{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "es2017", "module": "es2020", "lib": ["es2018", "dom"], "paths": {"core": ["dist/core/core", "dist/core"], "crud-core": ["dist/crud-core/crud-core", "dist/crud-core"], "dynamic-entity": ["dist/dynamic-entity/dynamic-entity", "dist/dynamic-entity"], "dynamic-forms": ["dist/dynamic-forms/dynamic-forms", "dist/dynamic-forms"], "query-builder": ["dist/query-builder/query-builder", "dist/query-builder"], "ui": ["dist/ui/ui", "dist/ui"]}}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}