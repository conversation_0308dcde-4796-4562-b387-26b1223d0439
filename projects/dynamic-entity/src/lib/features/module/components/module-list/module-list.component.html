<div class="p-6 h-full bg-gray-50">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900">
      {{ { en: "Modules", ar: "الوحدات" } | multiLangText }}
    </h1>
    <p class="text-sm text-gray-600 mt-1">
      {{ { en: "Manage your application modules", ar: "إدارة وحدات التطبيق" } | multiLangText }}
    </p>
  </div>

  <!-- Modules Grid -->
  <div *ngIf="!isLoading && !error && modules.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let module of modules; trackBy: trackByModuleId" class="bg-white rounded-lg border p-6">
      <h3 class="text-lg font-medium text-gray-700">
        {{ module.name | multiLangText }}
      </h3>
      <p class="text-sm text-gray-600 mt-2" *ngIf="module.description">
        {{ module.description | multiLangText }}
      </p>
      <div class="mt-4 text-xs text-gray-500">Sort Order: {{ module.sortOrder }}</div>
    </div>
  </div>
</div>
