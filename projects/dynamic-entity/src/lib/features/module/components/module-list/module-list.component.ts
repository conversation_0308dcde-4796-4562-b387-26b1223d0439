import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';
import { ModulesService } from '../../services/modules.service';
import { IModuleDto } from '../../interfaces';
import { IListRequest } from 'crud-core';
import { SortDirection } from 'query-builder';

@Component({
  selector: 'lib-module-list',
  templateUrl: './module-list.component.html',
  styleUrls: ['./module-list.component.scss'],
})
export class ModuleListComponent implements OnInit, OnDestroy {
  public isLoading = false;
  public modules: IModuleDto[] = [];
  public error: string | null = null;

  private readonly destroy$ = new Subject<void>();

  constructor(private readonly modulesService: ModulesService) {}

  public ngOnInit(): void {
    this.loadModules();
  }

  public ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  public loadModules(): void {
    this.isLoading = true;
    this.error = null;

    const listRequest: IListRequest = {
      pagination: { pageNumber: 1, pageSize: 50 },
      sort: [{ field: 'sortOrder', direction: SortDirection.Asc }],
    };

    this.modulesService
      .getList(listRequest)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoading = false))
      )
      .subscribe({
        next: (response) => {
          this.modules = response.items;
        },
        error: (error) => {
          console.error('Error loading modules:', error);
          this.error = 'Failed to load modules. Please try again.';
        },
      });
  }

  public onRetry(): void {
    this.loadModules();
  }

  public trackByModuleId(index: number, module: IModuleDto): string {
    return module.id;
  }
}
