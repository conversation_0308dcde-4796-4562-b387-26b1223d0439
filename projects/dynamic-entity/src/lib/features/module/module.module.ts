import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModuleDetailsComponent } from './components/module-details/module-details.component';
import { ModuleListComponent } from './components/module-list/module-list.component';
import { ModuleFormComponent } from './components/module-form/module-form.component';
import { ModulesService } from './services/modules.service';
import { UiModule } from 'ui';
import { CoreModule } from 'core';

@NgModule({
  declarations: [
    ModuleDetailsComponent,
    ModuleListComponent,
    ModuleFormComponent,
  ],
  imports: [CommonModule, CoreModule, UiModule],
  exports: [ModuleListComponent],
  providers: [ModulesService],
})
export class ModuleModule {}
