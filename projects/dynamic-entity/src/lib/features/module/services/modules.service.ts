import { Injectable } from '@angular/core';
import { HttpService } from 'core';
import { IModuleCreateDto, IModuleDto, IModuleUpdateDto } from '../interfaces';
import { HttpCrud } from 'crud-core';

@Injectable()
export class ModulesService extends HttpCrud<
  IModuleDto,
  IModuleCreateDto,
  IModuleUpdateDto
> {
  protected endpoint = 'modules';

  public constructor(http: HttpService) {
    super(http);
  }
}
