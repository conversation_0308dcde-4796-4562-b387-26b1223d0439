import { IBaseEntity, MultiLanguageText } from 'core';
import { IModule } from '../../../module/interfaces';

export interface IEntity extends IBaseEntity {
  name: MultiLanguageText;
  description?: MultiLanguageText;
  slug: string;
  sortOrder: number;
  sections: ISection[];
  moduleId: string;
  module: IModule;
}

export interface ISection {
  id: string;
  title: MultiLanguageText;
  description?: MultiLanguageText;
  rows: IRow[];
}

export interface IRow {
  id: string;
  columns: IColumn[];
}

export interface IColumn {
  id: string;
  fields: IField[];
}

export interface IField {
  id: string;
  name: string;
  type: FieldType;
  label: MultiLanguageText;
  description?: MultiLanguageText;
  helpText?: MultiLanguageText;
  placeholder?: MultiLanguageText;
  defaultValue?: any;
  validations?: IFieldValidation;
  configurations?: IFieldConfiguration;
  optionsSource?: IFieldOptionsSource;
  metadata?: Record<string, any>;
}

export enum FieldType {
  // Text Fields
  Text = 'Text',
  Email = 'Email',
  Password = 'Password',
  Url = 'Url',
  TextArea = 'TextArea',
  RichText = 'RichText',

  // Numeric Fields
  Integer = 'Integer',
  Decimal = 'Decimal',
  Percentage = 'Percentage',
  Currency = 'Currency',

  // Date & Time Fields
  Time = 'Time',
  Date = 'Date',
  DateTime = 'DateTime',

  // Selection Fields
  Select = 'Select',
  Radio = 'Radio',
  Checkbox = 'Checkbox',

  // File & Media Fields
  File = 'File',

  // Advanced Fields
  Json = 'Json',
  Color = 'Color',
  Rating = 'Rating',
  Location = 'Location',
  Coordinates = 'Coordinates',

  // Relationship Fields
  Reference = 'Reference',
}

export interface IFieldOptionsSource {
  type: OptionsSourceType;
  staticOptions?: IFieldOption[];
}

export enum OptionsSourceType {
  Static = 'Static',
}

export interface IFieldOption {
  id: string;
  label: MultiLanguageText;
  value?: any;
  description?: MultiLanguageText;
}

export interface IFieldConfiguration {
  viewModes: string[];
  isHidden: boolean;
  isReadOnly: boolean;
  isMultiValue: boolean;
  isMultiLanguage: boolean;
  isUnique: boolean;
  isSearchable: boolean;
  isFilterable: boolean;
  isSortable: boolean;
  isExportable: boolean;
  isHashable: boolean;
  isIndexable: boolean;
  isRange: boolean;

  // File-specific configurations
  allowedFileTypes: string[];
  maxFileSize?: number;
  maxFileCount?: number;
}

export interface IFieldValidation {
  isRequired: boolean;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  minDateTime?: Date;
  maxDateTime?: Date;

  // Range-specific validations
  minTimeSpan?: number; // milliseconds
  maxTimeSpan?: number; // milliseconds
  minDate?: string; // ISO date string
  maxDate?: string; // ISO date string

  patterns?: IValidationPattern;
  customValidations?: ICustomValidation;
}

export interface IValidationPattern {
  value: string;
  helpMessage?: MultiLanguageText;
  errorMessage?: MultiLanguageText;
}

export interface ICustomValidation {
  functionName: string;
  parameters?: Record<string, any>;
  helpMessage?: MultiLanguageText;
  errorMessage?: MultiLanguageText;
}
