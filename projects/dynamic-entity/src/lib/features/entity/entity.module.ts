import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EntityListComponent } from './components/entity-list/entity-list.component';
import { EntityFormComponent } from './components/entity-form/entity-form.component';
import { EntityDetailsComponent } from './components/entity-details/entity-details.component';
import { EntitiesService } from './services/entities.service';

@NgModule({
  declarations: [
    EntityListComponent,
    EntityFormComponent,
    EntityDetailsComponent,
  ],
  imports: [CommonModule],
  providers: [EntitiesService],
})
export class EntityModule {}
