import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DynamicEntityComponent } from './dynamic-entity.component';
import { DynamicEntityService } from './dynamic-entity.service';
import { CoreModule } from 'core';
import { ModuleModule } from './features/module/module.module';

@NgModule({
  declarations: [DynamicEntityComponent],
  imports: [CommonModule, CoreModule, ModuleModule],
  exports: [DynamicEntityComponent],
  providers: [DynamicEntityService],
})
export class DynamicEntityModule {}
