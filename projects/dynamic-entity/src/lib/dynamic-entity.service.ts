import { Injectable } from '@angular/core';
import { DialogTemplateService } from 'core';
import { ModuleListComponent } from './features/module/components/module-list/module-list.component';

const MODULE_ICON = `
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <rect x="3" y="3" width="7" height="7"></rect>
  <rect x="14" y="3" width="7" height="7"></rect>
  <rect x="14" y="14" width="7" height="7"></rect>
  <rect x="3" y="14" width="7" height="7"></rect>
</svg>
`;

@Injectable()
export class DynamicEntityService {
  public constructor(private readonly dialogTemplate: DialogTemplateService) {}

  public openDialog(): void {
    this.dialogTemplate.open({
      title: 'Modules',
      icon: { customIcon: MODULE_ICON },
      component: ModuleListComponent,
      width: '100vw',
      height: '100vh',
      cssClass: 'fullscreen-dialog',
    });
  }
}
