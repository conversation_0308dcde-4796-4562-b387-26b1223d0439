import { IHasId, IApiResponse, IListOf, HttpService } from 'core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  IListRequest,
  IOneRequest,
  IBaseRequest,
  ICountRequest,
  IExistsRequest,
  IUpdateRequest,
  IDeleteRequest,
  IRestoreRequest,
} from '../interfaces';
import { FilterOperator, IQueryResponse } from 'query-builder';

export abstract class HttpCrud<TDto, TCreateDto, TUpdateDto extends IHasId> {
  protected abstract endpoint: string;

  public constructor(protected http: HttpService) {}

  private apiUrl(endpoint: string): string {
    return `${this.endpoint}/${endpoint}`;
  }

  public getList(listRequest: IListRequest): Observable<IQueryResponse<TDto>> {
    return this.http
      .post<IApiResponse<IQueryResponse<TDto>>>(
        this.apiUrl('list'),
        listRequest
      )
      .pipe(map((response) => response.data));
  }

  public getListDynamic(
    listRequest: IListRequest
  ): Observable<IQueryResponse<any>> {
    return this.http
      .post<IApiResponse<IQueryResponse<any>>>(this.apiUrl('list'), listRequest)
      .pipe(map((response) => response.data));
  }

  public getOne(oneRequest: IOneRequest): Observable<TDto> {
    return this.http
      .post<IApiResponse<TDto>>(this.apiUrl('one'), oneRequest)
      .pipe(map((response) => response.data));
  }

  public getOneDynamic(oneRequest: IOneRequest): Observable<any> {
    return this.http
      .post<IApiResponse<any>>(this.apiUrl('one'), oneRequest)
      .pipe(map((response) => response.data));
  }

  public getById(
    id: string,
    getByIdRequest: Omit<IOneRequest, 'filters'> = {}
  ): Observable<TDto> {
    const oneRequest: IOneRequest = {
      ...getByIdRequest,
      ...this.createIdFilter(id),
    };
    return this.getOne(oneRequest);
  }

  public count(
    queries: Record<string, IBaseRequest>
  ): Observable<Record<string, number>> {
    const countRequest: ICountRequest = { queries };
    return this.http
      .post<IApiResponse<Record<string, number>>>(
        this.apiUrl('count'),
        countRequest
      )
      .pipe(map((response) => response.data));
  }

  public countSingle(baseRequest: IBaseRequest = {}): Observable<number> {
    return this.count({ count: baseRequest }).pipe(
      map((result) => result.count)
    );
  }

  public exists(
    queries: Record<string, IBaseRequest>
  ): Observable<Record<string, boolean>> {
    const existsRequest: IExistsRequest = { queries };
    return this.http
      .post<IApiResponse<Record<string, boolean>>>(
        this.apiUrl('exists'),
        existsRequest
      )
      .pipe(map((response) => response.data));
  }

  public existsSingle(baseRequest: IBaseRequest = {}): Observable<boolean> {
    return this.exists({ exists: baseRequest }).pipe(
      map((result) => result.exists)
    );
  }

  public existsById(id: string): Observable<boolean> {
    return this.existsSingle(this.createIdFilter(id));
  }

  public createRange(items: TCreateDto[]): Observable<TDto[]> {
    const createRangeDto: IListOf<TCreateDto> = { items };
    return this.http
      .post<IApiResponse<TDto[]>>(this.apiUrl('create'), createRangeDto)
      .pipe(map((response) => response.data));
  }

  public create(item: TCreateDto): Observable<TDto> {
    return this.createRange([item]).pipe(map((result) => result[0]));
  }

  public updateRange(items: TUpdateDto[]): Observable<TDto[]> {
    const updateRangeDto: IListOf<TUpdateDto> = { items };
    return this.http
      .put<IApiResponse<TDto[]>>(this.apiUrl('update'), updateRangeDto)
      .pipe(map((response) => response.data));
  }

  public update(item: TUpdateDto): Observable<TDto> {
    return this.updateRange([item]).pipe(map((result) => result[0]));
  }

  public updateByQuery(
    updates: Record<string, any>,
    query: IBaseRequest
  ): Observable<void> {
    const updateRequest: IUpdateRequest = {
      updates,
      query,
    };
    return this.http
      .put<IApiResponse<any>>(this.apiUrl('update-by-query'), updateRequest)
      .pipe(map(() => void 0));
  }

  public upsertRange(items: TCreateDto[]): Observable<void> {
    const rangeDto: IListOf<TCreateDto> = { items };
    return this.http
      .put<IApiResponse<any>>(this.apiUrl('upsert'), rangeDto)
      .pipe(map(() => void 0));
  }

  public upsert(item: TCreateDto): Observable<void> {
    return this.upsertRange([item]);
  }

  public deleteByQuery(
    query: IBaseRequest,
    hardDelete: boolean = false
  ): Observable<void> {
    const deleteRequest: IDeleteRequest = {
      query,
      hardDelete,
    };

    return this.http
      .delete<IApiResponse<any>>(this.apiUrl('delete-by-query'), deleteRequest)
      .pipe(map(() => void 0));
  }

  public deleteById(id: string, hardDelete: boolean = false): Observable<void> {
    return this.deleteByQuery(this.createIdFilter(id), hardDelete);
  }

  public restoreByQuery(query: IBaseRequest): Observable<void> {
    const restoreRequest: IRestoreRequest = {
      query,
    };
    return this.http
      .patch<IApiResponse<any>>(this.apiUrl('restore-by-query'), restoreRequest)
      .pipe(map(() => void 0));
  }

  public restoreById(id: string): Observable<void> {
    return this.restoreByQuery(this.createIdFilter(id));
  }

  private createIdFilter(id: string) {
    return {
      filters: {
        condition: { field: 'id', operator: FilterOperator.Eq, value: id },
      },
    };
  }
}
