import { Component } from '@angular/core';
import { Validators } from '@angular/forms';
import {
  FieldType,
  DynamicFormConfig,
  COMPREHENSIVE_FORM_CONFIG,
} from 'dynamic-forms';

@Component({
  selector: 'app-dynamic-form-examples',
  template: `
    <div class="container">
      <h1>Dynamic Form Example</h1>

      <lib-dynamic-form
        [config]="formConfig"
        [initialValues]="initialData"
        (formSubmit)="onFormSubmit($event)"
        (formChange)="onFormChange($event)"
      ></lib-dynamic-form>

      <div class="output" *ngIf="submittedData">
        <h3>Submitted Data:</h3>
        <pre>{{ submittedData | json }}</pre>
      </div>
    </div>
  `,
  styles: [
    `
      .container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 2rem;
      }

      h1 {
        color: #333;
        margin-bottom: 2rem;
      }

      .output {
        margin-top: 2rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 4px;
      }

      pre {
        margin: 0;
        white-space: pre-wrap;
      }
    `,
  ],
})
export class DynamicFormExamplesComponent {
  public submittedData: any = null;

  public formConfig: DynamicFormConfig = {
    fields: COMPREHENSIVE_FORM_CONFIG,
    submitLabel: 'Register',
    resetLabel: 'Clear Form',
  };

  public initialData = {
    firstName: 'John',
    email: '<EMAIL>',
    country: 'us',
    subscribe: true,
    address: {
      city: 'New York',
      state: 'NY',
    },
  };

  public onFormSubmit(data: any): void {
    console.log('Form submitted:', data);
    this.submittedData = data;

    // Here you would typically send the data to your backend
    alert('Form submitted successfully! Check the console for details.');
  }

  public onFormChange(formState: any): void {
    console.log('Form changed:', formState);
  }
}
