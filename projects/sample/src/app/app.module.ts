import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientModule } from '@angular/common/http';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { DynamicEntityModule } from 'dynamic-entity';
import { FabModule } from 'ui';
import {
  CoreModule,
  DialogModule,
  HttpModule,
  LanguageConfigModule,
} from 'core';
import { DynamicFormExamplesModule } from './examples/dynamic-form-examples/dynamic-form-examples.module';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    CoreModule.forRoot(),
    LanguageConfigModule.forRoot({ defaultLanguage: 'en' }),
    HttpModule.forRoot({
      config: { baseUrl: 'https://localhost:7069/api/v1' },
    }),
    DialogModule.forRoot(),
    DynamicEntityModule,
    FabModule,
    DynamicFormExamplesModule,
  ],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}
