import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// Components
import { DynamicFormComponent } from './components/dynamic-form/dynamic-form.component';
import { DynamicFieldComponent } from './components/dynamic-field/dynamic-field.component';

// Field Types - Basic inputs
import { InputFieldComponent } from './field-types/input/input-field.component';
import { EmailFieldComponent } from './field-types/email/email-field.component';
import { PasswordFieldComponent } from './field-types/password/password-field.component';
import { NumberFieldComponent } from './field-types/number/number-field.component';

// Field Types - Communication
import { UrlFieldComponent } from './field-types/url/url-field.component';
import { TelFieldComponent } from './field-types/tel/tel-field.component';

// Field Types - Date and time
import { DateFieldComponent } from './field-types/date/date-field.component';
import { TimeFieldComponent } from './field-types/time/time-field.component';

// Field Types - Selection
import { SelectFieldComponent } from './field-types/select/select-field.component';
import { RadioFieldComponent } from './field-types/radio/radio-field.component';
import { CheckboxFieldComponent } from './field-types/checkbox/checkbox-field.component';

// Field Types - Text areas
import { TextareaFieldComponent } from './field-types/textarea/textarea-field.component';

// Field Types - Special inputs
import { SearchFieldComponent } from './field-types/search/search-field.component';
import { ColorFieldComponent } from './field-types/color/color-field.component';
import { RangeFieldComponent } from './field-types/range/range-field.component';
import { FileFieldComponent } from './field-types/file/file-field.component';

// Field Types - Complex types
import { ArrayFieldComponent } from './field-types/array/array-field.component';

// Services
import { FormBuilderService } from './core/services/form-builder.service';

const FIELD_COMPONENTS = [
  // Basic inputs
  InputFieldComponent,
  EmailFieldComponent,
  PasswordFieldComponent,
  NumberFieldComponent,

  // Communication
  UrlFieldComponent,
  TelFieldComponent,

  // Date and time
  DateFieldComponent,
  TimeFieldComponent,

  // Selection
  SelectFieldComponent,
  RadioFieldComponent,
  CheckboxFieldComponent,

  // Text areas
  TextareaFieldComponent,

  // Special inputs
  SearchFieldComponent,
  ColorFieldComponent,
  RangeFieldComponent,
  FileFieldComponent,

  // Complex types
  ArrayFieldComponent,
];

const COMPONENTS = [
  DynamicFormComponent,
  DynamicFieldComponent,
  ...FIELD_COMPONENTS,
];

@NgModule({
  declarations: [...COMPONENTS],
  imports: [CommonModule, ReactiveFormsModule],
  exports: [DynamicFormComponent],
  providers: [FormBuilderService],
})
export class DynamicFormsModule {}
