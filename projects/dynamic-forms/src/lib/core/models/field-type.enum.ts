export enum FieldType {
  // Basic input types
  INPUT = 'input',
  TEXT = 'text',
  EMAIL = 'email',
  PASSWORD = 'password',
  NUMBER = 'number',

  // URL and communication
  URL = 'url',
  TEL = 'tel',

  // Date and time types
  DATE = 'date',
  TIME = 'time',
  DATETIME_LOCAL = 'datetime-local',
  MONTH = 'month',
  WEEK = 'week',

  // Selection types
  SELECT = 'select',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',

  // Text areas
  TEXTAREA = 'textarea',

  // Special input types
  SEARCH = 'search',
  COLOR = 'color',
  RANGE = 'range',
  FILE = 'file',

  // Complex types
  GROUP = 'group',
  ARRAY = 'array',
}
