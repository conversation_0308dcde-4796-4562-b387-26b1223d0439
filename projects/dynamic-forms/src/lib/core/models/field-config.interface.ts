import { ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { FieldType } from './field-type.enum';

export interface FieldConfig {
  key: string;
  type: FieldType;
  label?: string;
  placeholder?: string;
  value?: any;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  hidden?: boolean;
  options?: OptionItem[]; // For select, radio
  validators?: ValidatorFn[];
  asyncValidators?: AsyncValidatorFn[];
  errorMessages?: { [key: string]: string };
  cssClass?: string;
  hint?: string;

  // For group type: children fields
  fieldGroup?: FieldConfig[];

  // For array type: template for each array item (either a primitive field config or a group config)
  fieldArray?: FieldConfig;

  // Array-specific properties
  minItems?: number; // Minimum number of array items
  maxItems?: number; // Maximum number of array items
  addButtonText?: string; // Text for the add button
  emptyMessage?: string; // Message when array is empty
  showItemCount?: boolean; // Whether to show item count
}

export interface OptionItem {
  label: string;
  value: any;
  disabled?: boolean;
}
