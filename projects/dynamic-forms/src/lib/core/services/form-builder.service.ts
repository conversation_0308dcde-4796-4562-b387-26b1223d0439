// core/services/form-builder.service.ts
import { Injectable } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormControl,
  ValidatorFn,
  AsyncValidatorFn,
  Validators,
} from '@angular/forms';
import { FieldConfig } from '../models/field-config.interface';
import { FieldType } from '../models/field-type.enum';

@Injectable()
export class FormBuilderService {
  constructor(private readonly fb: FormBuilder) {}

  // Map for type-specific validators (extendable)
  private readonly TYPE_VALIDATORS: Partial<Record<FieldType, ValidatorFn[]>> =
    {
      [FieldType.EMAIL]: [Validators.email],
      [FieldType.NUMBER]: [Validators.pattern(/^\d+$/)],
    };

  public createForm(fields: FieldConfig[] = []): FormGroup {
    const controls: { [key: string]: any } = {};

    fields.forEach((field) => {
      switch (field.type) {
        case FieldType.GROUP:
          controls[field.key] = this.createForm(field.fieldGroup || []);
          break;

        case FieldType.ARRAY:
          if (!field.fieldArray)
            throw new Error(
              `Array field "${field.key}" missing fieldArray template`
            );
          controls[field.key] = this.createFormArray(field);
          break;

        default:
          controls[field.key] = this.createControl(field);
      }
    });

    return this.fb.group(controls);
  }

  public createControl(field: FieldConfig): FormControl {
    const validators: ValidatorFn[] = this.getValidators(field);
    const asyncValidators: AsyncValidatorFn[] = field.asyncValidators || [];

    const control = this.fb.control(
      { value: field.value ?? null, disabled: !!field.disabled },
      { validators, asyncValidators }
    );

    return control;
  }

  private getValidators(field: FieldConfig): ValidatorFn[] {
    const validators: ValidatorFn[] = [...(field.validators || [])];
    if (field.required) validators.push(Validators.required);

    const typeValidators = this.TYPE_VALIDATORS[field.type];
    if (typeValidators) validators.push(...typeValidators);

    return validators;
  }

  public createFormArray(field: FieldConfig): FormArray {
    const template = field.fieldArray!;
    const arr = this.fb.array([]);

    // initial values from field.value (if provided)
    const initialValues: any[] = Array.isArray(field.value) ? field.value : [];

    // decide how many initial items to create: max(initialValues.length, minItems)
    const initialCount = Math.max(initialValues.length, field.minItems ?? 0);

    for (let i = 0; i < initialCount; i++) {
      arr.push(this.createArrayItemControl(template, initialValues[i]));
    }

    return arr;
  }

  public createArrayItemControl(
    fieldConfig: FieldConfig,
    value?: any
  ): FormGroup | FormControl {
    // complex: array item is a group (object)
    if (fieldConfig.fieldGroup && fieldConfig.fieldGroup.length > 0) {
      const grp = this.createForm(fieldConfig.fieldGroup);
      if (value && typeof value === 'object') grp.patchValue(value);
      return grp;
    }

    // primitive item: create simple control using the template field's validators, type, etc.
    const cfg = { ...fieldConfig, value: value ?? fieldConfig.value };
    return this.createControl(cfg);
  }

  // Add an item to a FormArray. Pass the *parent* array FieldConfig (so we can read template/min/max).
  public addArrayItem(
    formArray: FormArray,
    arrayFieldConfig: FieldConfig,
    value?: any
  ): void {
    if (!arrayFieldConfig?.fieldArray) {
      throw new Error(
        'addArrayItem requires arrayFieldConfig with fieldArray template'
      );
    }

    const max = arrayFieldConfig.maxItems;
    if (typeof max === 'number' && formArray.length >= max) {
      return; // respect maxItems
    }

    const newItem = this.createArrayItemControl(
      arrayFieldConfig.fieldArray,
      value
    );
    formArray.push(newItem);
    // mark the new control as touched to surface validation immediately
    (newItem as any).markAsTouched?.();
  }

  public removeArrayItem(
    formArray: FormArray,
    index: number,
    minItems?: number
  ): void {
    if (index < 0 || index >= formArray.length) return;
    if (typeof minItems === 'number' && formArray.length <= minItems) return; // respect minItems
    formArray.removeAt(index);
    formArray.markAsTouched();
  }

  public updateFieldValue(form: FormGroup, fieldKey: string, value: any): void {
    const control = form.get(fieldKey);
    if (control) {
      control.setValue(value);
      control.markAsTouched();
    }
  }

  public resetForm(form: FormGroup): void {
    form.reset();
    form.markAsUntouched();
  }

  public getFieldErrors(form: FormGroup, fieldKey: string): string[] {
    const control = form.get(fieldKey);
    if (!control?.errors || !control.touched) return [];
    return Object.keys(control.errors);
  }
}
