import { Validators } from '@angular/forms';
import { FieldConfig } from '../core/models/field-config.interface';
import { FieldType } from '../core/models/field-type.enum';

/**
 * Examples demonstrating different array field use cases
 */

// Example 1: Simple array of text values (skills, tags, etc.)
export const SIMPLE_ARRAY_EXAMPLE: FieldConfig = {
  key: 'skills',
  type: FieldType.ARRAY,
  label: 'Skills',
  hint: 'Add your technical skills',
  fieldArray: {
    key: 'skill',
    type: FieldType.TEXT,
    label: 'Skill',
    placeholder: 'e.g., JavaScript, Python, React',
    required: true,
    validators: [Validators.required, Validators.minLength(2)],
  },
  // Array-specific configuration
  minItems: 1,
  maxItems: 10,
  addButtonText: 'Add Skill',
  emptyMessage: 'No skills added yet. Add your first skill!',
  value: ['JavaScript', 'TypeScript'], // Default values
};

// Example 2: Array of email addresses
export const EMAIL_ARRAY_EXAMPLE: FieldConfig = {
  key: 'contactEmails',
  type: FieldType.ARRAY,
  label: 'Contact Email Addresses',
  hint: 'Add multiple email addresses for contact',
  fieldArray: {
    key: 'email',
    type: FieldType.EMAIL,
    label: 'Email Address',
    placeholder: '<EMAIL>',
    required: true,
    validators: [Validators.required, Validators.email],
  },
  minItems: 1,
  maxItems: 5,
  addButtonText: 'Add Email',
  emptyMessage: 'No email addresses added',
};

// Example 3: Array of phone numbers
export const PHONE_ARRAY_EXAMPLE: FieldConfig = {
  key: 'phoneNumbers',
  type: FieldType.ARRAY,
  label: 'Phone Numbers',
  hint: 'Add multiple phone numbers',
  fieldArray: {
    key: 'phone',
    type: FieldType.TEL,
    label: 'Phone Number',
    placeholder: '+****************',
    validators: [Validators.pattern(/^[\+]?[1-9][\d]{0,15}$/)],
  },
  maxItems: 3,
  addButtonText: 'Add Phone',
  emptyMessage: 'No phone numbers added',
};

// Example 4: Complex array with nested objects (addresses)
export const COMPLEX_ARRAY_EXAMPLE: FieldConfig = {
  key: 'addresses',
  type: FieldType.ARRAY,
  label: 'Addresses',
  hint: 'Add multiple addresses (home, work, etc.)',
  fieldArray: {
    key: 'address',
    type: FieldType.GROUP,
    label: 'Address',
    fieldGroup: [
      {
        key: 'type',
        type: FieldType.SELECT,
        label: 'Address Type',
        required: true,
        options: [
          { label: 'Home', value: 'home' },
          { label: 'Work', value: 'work' },
          { label: 'Other', value: 'other' },
        ],
        validators: [Validators.required],
      },
      {
        key: 'street',
        type: FieldType.TEXT,
        label: 'Street Address',
        placeholder: '123 Main St',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'city',
        type: FieldType.TEXT,
        label: 'City',
        placeholder: 'New York',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'state',
        type: FieldType.TEXT,
        label: 'State/Province',
        placeholder: 'NY',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'zipCode',
        type: FieldType.TEXT,
        label: 'ZIP/Postal Code',
        placeholder: '10001',
        required: true,
        validators: [
          Validators.required,
          Validators.pattern(/^\d{5}(-\d{4})?$/),
        ],
      },
    ],
  },
  minItems: 1,
  maxItems: 3,
  addButtonText: 'Add Address',
  emptyMessage: 'No addresses added. Please add at least one address.',
  showItemCount: true,
};

// Example 5: Array of work experience entries
export const WORK_EXPERIENCE_ARRAY: FieldConfig = {
  key: 'workExperience',
  type: FieldType.ARRAY,
  label: 'Work Experience',
  hint: 'Add your work experience history',
  fieldArray: {
    key: 'experience',
    type: FieldType.GROUP,
    label: 'Work Experience',
    fieldGroup: [
      {
        key: 'company',
        type: FieldType.TEXT,
        label: 'Company Name',
        placeholder: 'Acme Corp',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'position',
        type: FieldType.TEXT,
        label: 'Position',
        placeholder: 'Software Developer',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'startDate',
        type: FieldType.DATE,
        label: 'Start Date',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'endDate',
        type: FieldType.DATE,
        label: 'End Date',
        hint: 'Leave empty if current position',
      },
      {
        key: 'description',
        type: FieldType.TEXTAREA,
        label: 'Job Description',
        placeholder: 'Describe your responsibilities and achievements...',
        validators: [Validators.maxLength(500)],
      },
    ],
  },
  maxItems: 10,
  addButtonText: 'Add Work Experience',
  emptyMessage: 'No work experience added yet',
};

// Example 6: Array with validation constraints
export const VALIDATED_ARRAY_EXAMPLE: FieldConfig = {
  key: 'teamMembers',
  type: FieldType.ARRAY,
  label: 'Team Members',
  hint: 'Add team members (minimum 2, maximum 8)',
  required: true,
  fieldArray: {
    key: 'member',
    type: FieldType.GROUP,
    label: 'Team Member',
    fieldGroup: [
      {
        key: 'name',
        type: FieldType.TEXT,
        label: 'Full Name',
        placeholder: 'John Doe',
        required: true,
        validators: [Validators.required, Validators.minLength(2)],
      },
      {
        key: 'email',
        type: FieldType.EMAIL,
        label: 'Email',
        placeholder: '<EMAIL>',
        required: true,
        validators: [Validators.required, Validators.email],
      },
      {
        key: 'role',
        type: FieldType.SELECT,
        label: 'Role',
        required: true,
        options: [
          { label: 'Developer', value: 'developer' },
          { label: 'Designer', value: 'designer' },
          { label: 'Manager', value: 'manager' },
          { label: 'QA Tester', value: 'qa' },
        ],
        validators: [Validators.required],
      },
    ],
  },
  minItems: 2,
  maxItems: 8,
  addButtonText: 'Add Team Member',
  emptyMessage: 'No team members added. Add at least 2 team members.',
  showItemCount: true,
};

// Complete form example with multiple array types
export const COMPREHENSIVE_ARRAY_FORM: FieldConfig[] = [
  {
    key: 'personalInfo',
    type: FieldType.GROUP,
    label: 'Personal Information',
    fieldGroup: [
      {
        key: 'firstName',
        type: FieldType.TEXT,
        label: 'First Name',
        required: true,
        validators: [Validators.required],
      },
      {
        key: 'lastName',
        type: FieldType.TEXT,
        label: 'Last Name',
        required: true,
        validators: [Validators.required],
      },
    ],
  },
  SIMPLE_ARRAY_EXAMPLE,
  EMAIL_ARRAY_EXAMPLE,
  PHONE_ARRAY_EXAMPLE,
  COMPLEX_ARRAY_EXAMPLE,
  WORK_EXPERIENCE_ARRAY,
];
