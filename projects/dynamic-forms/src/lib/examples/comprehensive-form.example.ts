import { Validators } from '@angular/forms';
import { FieldConfig } from '../core/models/field-config.interface';
import { FieldType } from '../core/models/field-type.enum';

/**
 * Comprehensive example demonstrating all available field types
 * in the dynamic forms library.
 */
export const COMPREHENSIVE_FORM_CONFIG: FieldConfig[] = [
  // Basic text inputs
  {
    key: 'firstName',
    type: FieldType.TEXT,
    label: 'First Name',
    placeholder: 'Enter your first name',
    required: true,
    validators: [Validators.required, Validators.minLength(2)],
    hint: 'At least 2 characters required',
  },

  {
    key: 'email',
    type: FieldType.EMAIL,
    label: 'Email Address',
    placeholder: '<EMAIL>',
    required: true,
    validators: [Validators.required, Validators.email],
    hint: 'We will never share your email',
  },

  {
    key: 'password',
    type: FieldType.PASSWORD,
    label: 'Password',
    placeholder: 'Enter a secure password',
    required: true,
    validators: [Validators.required, Validators.minLength(8)],
    hint: 'Minimum 8 characters',
  },

  // Communication fields
  {
    key: 'website',
    type: FieldType.URL,
    label: 'Website',
    placeholder: 'https://yourwebsite.com',
    validators: [Validators.pattern(/^https?:\/\/.+/)],
    hint: 'Include http:// or https://',
  },

  {
    key: 'phone',
    type: FieldType.TEL,
    label: 'Phone Number',
    placeholder: '+****************',
    validators: [Validators.pattern(/^[\+]?[1-9][\d]{0,15}$/)],
    hint: 'International format preferred',
  },

  // Numeric fields
  {
    key: 'age',
    type: FieldType.NUMBER,
    label: 'Age',
    placeholder: '25',
    validators: [Validators.min(18), Validators.max(120)],
    hint: 'Must be between 18 and 120',
  },

  {
    key: 'satisfaction',
    type: FieldType.RANGE,
    label: 'Satisfaction Level',
    value: 5,
    hint: 'Rate your satisfaction from 1 to 10',
  },

  // Date and time fields
  {
    key: 'birthDate',
    type: FieldType.DATE,
    label: 'Birth Date',
    required: true,
    validators: [Validators.required],
    hint: 'Select your date of birth',
  },

  {
    key: 'appointmentTime',
    type: FieldType.TIME,
    label: 'Preferred Appointment Time',
    hint: 'Select your preferred time',
  },

  {
    key: 'eventDateTime',
    type: FieldType.DATETIME_LOCAL,
    label: 'Event Date & Time',
    hint: 'When should the event start?',
  },

  {
    key: 'targetMonth',
    type: FieldType.MONTH,
    label: 'Target Month',
    hint: 'Which month are you targeting?',
  },

  {
    key: 'workWeek',
    type: FieldType.WEEK,
    label: 'Work Week',
    hint: 'Select the work week',
  },

  // Selection fields
  {
    key: 'country',
    type: FieldType.SELECT,
    label: 'Country',
    required: true,
    options: [
      { label: 'United States', value: 'US' },
      { label: 'Canada', value: 'CA' },
      { label: 'United Kingdom', value: 'UK' },
      { label: 'Germany', value: 'DE' },
      { label: 'France', value: 'FR' },
    ],
    validators: [Validators.required],
    hint: 'Select your country',
  },

  {
    key: 'gender',
    type: FieldType.RADIO,
    label: 'Gender',
    options: [
      { label: 'Male', value: 'male' },
      { label: 'Female', value: 'female' },
      { label: 'Other', value: 'other' },
      { label: 'Prefer not to say', value: 'not_specified' },
    ],
    hint: 'Select your gender',
  },

  {
    key: 'newsletter',
    type: FieldType.CHECKBOX,
    label: 'Subscribe to Newsletter',
    value: false,
    hint: 'Receive updates and news',
  },

  // Text area
  {
    key: 'bio',
    type: FieldType.TEXTAREA,
    label: 'Biography',
    placeholder: 'Tell us about yourself...',
    validators: [Validators.maxLength(500)],
    hint: 'Maximum 500 characters',
  },

  // Special input types
  {
    key: 'searchTerm',
    type: FieldType.SEARCH,
    label: 'Search',
    placeholder: 'Search for something...',
    hint: 'Enter your search query',
  },

  {
    key: 'favoriteColor',
    type: FieldType.COLOR,
    label: 'Favorite Color',
    value: '#007bff',
    hint: 'Pick your favorite color',
  },

  // Array examples
  {
    key: 'skills',
    type: FieldType.ARRAY,
    label: 'Technical Skills',
    hint: 'Add your technical skills',
    fieldArray: {
      key: 'skill',
      type: FieldType.TEXT,
      label: 'Skill',
      placeholder: 'e.g., JavaScript, Python, React',
      required: true,
      validators: [Validators.required, Validators.minLength(2)],
    },
    minItems: 1,
    maxItems: 10,
    addButtonText: 'Add Skill',
    emptyMessage: 'No skills added yet',
    value: ['JavaScript', 'TypeScript'],
  },

  {
    key: 'socialLinks',
    type: FieldType.ARRAY,
    label: 'Social Media Links',
    hint: 'Add your social media profiles',
    fieldArray: {
      key: 'url',
      type: FieldType.URL,
      label: 'Social Media URL',
      placeholder: 'https://linkedin.com/in/yourprofile',
      validators: [Validators.pattern(/^https?:\/\/.+/)],
    },
    maxItems: 5,
    addButtonText: 'Add Social Link',
    emptyMessage: 'No social links added',
  },
];
