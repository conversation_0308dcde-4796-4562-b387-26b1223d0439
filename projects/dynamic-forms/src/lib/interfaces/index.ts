export interface IFormConfig {
  id: string;
  version?: string;
  metadata?: IFormMetadata;
  settings?: IFormSettings;
  layout: IFormLayout;
  conditionals?: IConditionalRule[];
  hooks?: IFormHooks;
  validation?: IFormValidation;
  i18n?: IFormI18n;
}

export interface IFormMetadata {
  title?: string;
  description?: string;
  author?: string;
  createdAt?: string;
  updatedAt?: string;
  tags?: string[];
  category?: string;
  estimatedTime?: string;
  icon?: string;
}

export interface IFormSettings {
  autoSave?: boolean;
  autoSaveInterval?: number;
  saveProgress?: boolean;
  allowDrafts?: boolean;
  showProgressBar?: boolean;
  showStepIndicator?: boolean;
  allowNavigation?: boolean;
  validateOnBlur?: boolean;
  validateOnChange?: boolean;
  submitOnEnter?: boolean;
  resetAfterSubmit?: boolean;
  confirmBeforeLeave?: boolean;
  scrollToError?: boolean;
  focusFirstError?: boolean;
  persistState?: boolean;
  stateKey?: string;
  theme?: string;
  className?: string;
  readonly?: boolean;
  disabled?: boolean;
  debug?: boolean;
}

export interface IFormLayout {
  type: 'single' | 'wizard' | 'tabs' | 'accordion' | 'sections' | 'grid';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  style?: IStyleConfig;
  responsive?: IResponsiveConfig;
  animation?: IAnimationConfig;

  // For single page forms
  sections?: ISection[];

  // For wizard forms
  steps?: IWizardStep[];

  // For tabbed forms
  tabs?: ITab[];

  // For accordion forms
  panels?: IAccordionPanel[];

  // For grid layout
  grid?: IGridConfig;
}

export interface IWizardStep {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  className?: string;
  validation?: IStepValidation;
  condition?: ICondition;
  sections: ISection[];

  // Step-specific settings
  optional?: boolean;
  skippable?: boolean;
  completionCriteria?: ICompletionCriteria;
  navigationButtons?: {
    back?: IButtonConfig;
    next?: IButtonConfig;
    skip?: IButtonConfig;
  };
}

export interface ITab {
  id: string;
  title: string;
  icon?: string;
  badge?: string | number;
  disabled?: boolean;
  hidden?: boolean;
  condition?: ICondition;
  lazy?: boolean;
  sections: ISection[];
}

export interface IAccordionPanel {
  id: string;
  title: string;
  subtitle?: string;
  icon?: string;
  expanded?: boolean;
  disabled?: boolean;
  condition?: ICondition;
  sections: ISection[];
}

export interface ISection {
  id: string;
  title?: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  className?: string;
  style?: IStyleConfig;
  collapsible?: boolean;
  collapsed?: boolean;
  hidden?: boolean;
  condition?: ICondition;
  validation?: ISectionValidation;
  rows: IRow[];
}

export interface IRow {
  id?: string;
  className?: string;
  style?: IStyleConfig;
  gap?: number | string;
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around';
  hidden?: boolean;
  condition?: ICondition;
  columns: IColumn[];
}

export interface IColumn {
  id?: string;
  width?: number | IResponsiveWidth;
  offset?: number | IResponsiveOffset;
  className?: string;
  style?: IStyleConfig;
  align?: 'left' | 'center' | 'right';
  hidden?: boolean;
  condition?: ICondition;

  // Fields are directly embedded here
  fields: IField[];
}

export interface IField {
  // Core Properties
  id: string;
  name: string;
  type: FieldType;

  // Display Properties
  label?: string;
  placeholder?: string;
  helpText?: string;
  tooltip?: string;
  icon?: string;
  prefix?: string;
  suffix?: string;

  // Value Properties
  defaultValue?: any;
  value?: any;

  // State Properties
  required?: boolean | ICondition;
  disabled?: boolean | ICondition;
  readonly?: boolean | ICondition;
  hidden?: boolean | ICondition;

  // Validation
  validation?: IValidationRule[];
  asyncValidation?: IAsyncValidationRule[];

  // Styling
  className?: string;
  style?: IStyleConfig;
  labelPosition?: 'top' | 'left' | 'right' | 'floating' | 'inline';
  size?: 'small' | 'medium' | 'large';
  variant?: string;

  // Events
  events?: IFieldEvents;

  // Dependencies
  dependencies?: IFieldDependency[];

  // Conditional
  condition?: ICondition;
  conditionalRules?: IConditionalRule[];

  // Transformations
  transform?: ITransformConfig;

  // Accessibility
  accessibility?: IAccessibilityConfig;

  // Field-specific configurations based on type
  // For text fields
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  mask?: string;
  autocomplete?: string;
  spellcheck?: boolean;

  // For number fields
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  thousandSeparator?: string;
  decimalSeparator?: string;

  // For select/multiselect
  options?: IOption[] | IDataSource;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  maxSelection?: number;
  minSelection?: number;

  // For date/time fields
  dateConfig?: IDateConfig;

  // For file upload
  fileConfig?: IFileConfig;

  // For array fields
  arrayConfig?: IArrayConfig;

  // For group fields
  groupConfig?: IGroupConfig;

  // For rich text
  editorConfig?: IEditorConfig;

  // For custom components
  customComponent?: string;
  customProps?: Record<string, any>;
}

export interface IGroupConfig {
  layout?: 'vertical' | 'horizontal' | 'grid';
  collapsible?: boolean;
  collapsed?: boolean;
  bordered?: boolean;
  // Nested structure - groups contain rows
  rows: IRow[];
}

export interface IArrayConfig {
  minItems?: number;
  maxItems?: number;
  initialItems?: number;
  addButton?: IButtonConfig;
  removeButton?: IButtonConfig;
  sortable?: boolean;
  collapsible?: boolean;
  duplicatable?: boolean;
  // Template defines the structure for each array item
  template: {
    rows: IRow[];
  };
}

// Continue with missing interfaces...

export interface IResponsiveWidth {
  xs?: number; // 0-575px
  sm?: number; // 576-767px
  md?: number; // 768-991px
  lg?: number; // 992-1199px
  xl?: number; // 1200-1399px
  xxl?: number; // 1400px+
}

export interface IResponsiveOffset {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  xxl?: number;
}

export interface IResponsiveConfig {
  breakpoints?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
  containerWidth?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    xxl?: string;
  };
}

export interface IStyleConfig {
  // Layout
  display?: string;
  position?: string;
  zIndex?: number;
  overflow?: string;
  float?: string;
  clear?: string;

  // Spacing
  margin?: string | number;
  marginTop?: string | number;
  marginRight?: string | number;
  marginBottom?: string | number;
  marginLeft?: string | number;
  padding?: string | number;
  paddingTop?: string | number;
  paddingRight?: string | number;
  paddingBottom?: string | number;
  paddingLeft?: string | number;

  // Sizing
  width?: string | number;
  minWidth?: string | number;
  maxWidth?: string | number;
  height?: string | number;
  minHeight?: string | number;
  maxHeight?: string | number;

  // Typography
  fontSize?: string | number;
  fontWeight?: string | number;
  fontFamily?: string;
  lineHeight?: string | number;
  textAlign?: string;
  textTransform?: string;
  letterSpacing?: string | number;

  // Colors
  color?: string;
  backgroundColor?: string;
  opacity?: number;

  // Borders
  border?: string;
  borderTop?: string;
  borderRight?: string;
  borderBottom?: string;
  borderLeft?: string;
  borderRadius?: string | number;
  borderColor?: string;
  borderStyle?: string;
  borderWidth?: string | number;

  // Shadows & Effects
  boxShadow?: string;
  textShadow?: string;
  filter?: string;
  backdropFilter?: string;

  // Flexbox
  flex?: string | number;
  flexDirection?: string;
  flexWrap?: string;
  justifyContent?: string;
  alignItems?: string;
  alignContent?: string;
  gap?: string | number;

  // Grid
  gridTemplateColumns?: string;
  gridTemplateRows?: string;
  gridGap?: string | number;
  gridColumn?: string;
  gridRow?: string;

  // Transitions & Animations
  transition?: string;
  transform?: string;
  animation?: string;

  // Custom CSS
  [key: string]: any;
}

export interface IAnimationConfig {
  type?: 'fade' | 'slide' | 'zoom' | 'flip' | 'rotate' | 'bounce' | 'custom';
  duration?: number;
  delay?: number;
  easing?: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | string;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  iterationCount?: number | 'infinite';
  playState?: 'running' | 'paused';
  customKeyframes?: string;
}

export interface IGridConfig {
  columns?: number | string;
  rows?: number | string;
  gap?: number | string;
  columnGap?: number | string;
  rowGap?: number | string;
  areas?: string[];
  autoFlow?: 'row' | 'column' | 'dense' | 'row dense' | 'column dense';
  autoColumns?: string;
  autoRows?: string;
  justifyItems?: 'start' | 'end' | 'center' | 'stretch';
  alignItems?: 'start' | 'end' | 'center' | 'stretch';
  justifyContent?:
    | 'start'
    | 'end'
    | 'center'
    | 'stretch'
    | 'space-around'
    | 'space-between'
    | 'space-evenly';
  alignContent?:
    | 'start'
    | 'end'
    | 'center'
    | 'stretch'
    | 'space-around'
    | 'space-between'
    | 'space-evenly';
}

type FieldType =
  // Text inputs
  | 'text'
  | 'textarea'
  | 'email'
  | 'password'
  | 'tel'
  | 'url'
  | 'search'

  // Number inputs
  | 'number'
  | 'range'
  | 'rating'
  | 'slider'
  | 'currency'
  | 'percentage'

  // Date & Time
  | 'date'
  | 'datetime'
  | 'time'
  | 'month'
  | 'week'
  | 'year'
  | 'daterange'
  | 'timerange'

  // Selection
  | 'select'
  | 'multiselect'
  | 'radio'
  | 'checkbox'
  | 'toggle'
  | 'switch'
  | 'buttongroup'

  // File & Media
  | 'file'
  | 'image'
  | 'video'
  | 'audio'
  | 'document'
  | 'avatar'

  // Advanced inputs
  | 'color'
  | 'autocomplete'
  | 'tags'
  | 'chips'
  | 'otp'
  | 'pin'
  | 'pattern'
  | 'signature'
  | 'location'
  | 'coordinates'

  // Rich content
  | 'richtext'
  | 'markdown'
  | 'code'
  | 'json'
  | 'html'

  // Structural
  | 'group'
  | 'array'
  | 'repeater'
  | 'matrix'
  | 'fieldset'

  // Display
  | 'heading'
  | 'paragraph'
  | 'divider'
  | 'spacer'
  | 'alert'
  | 'info'

  // Special
  | 'calculated'
  | 'hidden'
  | 'custom'
  | 'component'
  | 'template';

export interface IOption {
  value: any;
  label: string;
  disabled?: boolean;
  hidden?: boolean;
  icon?: string;
  description?: string;
  metadata?: Record<string, any>;
  group?: string;
  color?: string;
  badge?: string | number;
}

export interface IDataSource {
  type: 'static' | 'api' | 'function' | 'observable';

  // For static data
  data?: IOption[];

  // For API calls
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  body?: any;

  // Response mapping
  valueField?: string;
  labelField?: string;
  descriptionField?: string;
  iconField?: string;
  disabledField?: string;
  groupField?: string;

  // For search/filter
  searchParam?: string;
  searchMinLength?: number;
  searchDebounce?: number;

  // Pagination
  paginated?: boolean;
  pageSize?: number;
  pageParam?: string;

  // Caching
  cache?: boolean;
  cacheKey?: string;
  cacheDuration?: number;

  // Transform
  transform?: (data: any) => IOption[];

  // For function data source
  function?: (...args: any[]) => IOption[] | Promise<IOption[]>;

  // For observable data source
  observable?: any; // Observable<IOption[]>

  // Error handling
  onError?: (error: any) => void;
  fallbackData?: IOption[];
}

export interface IValidationRule {
  type:
    | 'required'
    | 'email'
    | 'url'
    | 'pattern'
    | 'minLength'
    | 'maxLength'
    | 'min'
    | 'max'
    | 'minDate'
    | 'maxDate'
    | 'minAge'
    | 'maxAge'
    | 'minItems'
    | 'maxItems'
    | 'fileSize'
    | 'fileType'
    | 'dimensions'
    | 'custom'
    | 'regex'
    | 'alphanumeric'
    | 'numeric'
    | 'alpha'
    | 'creditCard'
    | 'phone'
    | 'ssn'
    | 'ein'
    | 'ipAddress'
    | 'macAddress';

  // Validation parameters
  value?: any;
  pattern?: string | RegExp;
  message?: string;

  // For complex validations
  params?: Record<string, any>;

  // Custom validator function
  validator?: (
    value: any,
    field: IField,
    form: any
  ) => boolean | string | Promise<boolean | string>;

  // When to validate
  validateOn?: 'blur' | 'change' | 'submit';

  // Conditional validation
  condition?: ICondition;

  // Severity
  severity?: 'error' | 'warning' | 'info';
}

export interface IAsyncValidationRule {
  type: 'unique' | 'exists' | 'api' | 'custom';
  endpoint?: string;
  method?: 'GET' | 'POST';
  headers?: Record<string, string>;
  params?: Record<string, any>;

  // Debouncing
  debounce?: number;

  // Custom async validator
  validator?: (
    value: any,
    field: IField,
    form: any
  ) => Promise<boolean | string>;

  // Response handling
  successField?: string;
  errorField?: string;

  message?: string;

  // Loading state
  loadingMessage?: string;

  // Cache results
  cache?: boolean;
  cacheKey?: string;
}

export interface ICondition {
  // Simple conditions
  field?: string;
  operator?:
    | 'equals'
    | 'notEquals'
    | 'contains'
    | 'notContains'
    | 'startsWith'
    | 'endsWith'
    | 'isEmpty'
    | 'notEmpty'
    | 'greaterThan'
    | 'lessThan'
    | 'greaterOrEqual'
    | 'lessOrEqual'
    | 'between'
    | 'notBetween'
    | 'in'
    | 'notIn'
    | 'regex'
    | 'truthy'
    | 'falsy';
  value?: any;

  // Complex conditions
  and?: ICondition[];
  or?: ICondition[];
  not?: ICondition;

  // Custom condition function
  custom?: (form: any) => boolean;

  // JavaScript expression
  expression?: string;
}

export interface IConditionalRule {
  id: string;
  name?: string;
  description?: string;
  priority?: number;
  enabled?: boolean;

  condition: ICondition;

  actions: IAction[];

  // When to evaluate
  trigger?: 'immediate' | 'blur' | 'change' | 'submit';

  // Debouncing
  debounce?: number;
}

export interface IAction {
  type:
    | 'show'
    | 'hide'
    | 'enable'
    | 'disable'
    | 'require'
    | 'unrequire'
    | 'setValue'
    | 'clearValue'
    | 'setOptions'
    | 'clearOptions'
    | 'addClass'
    | 'removeClass'
    | 'setStyle'
    | 'removeStyle'
    | 'validate'
    | 'clearValidation'
    | 'setError'
    | 'clearError'
    | 'focus'
    | 'blur'
    | 'scroll'
    | 'highlight'
    | 'custom';

  target: string | string[];

  // Action parameters
  value?: any;
  options?: IOption[];
  className?: string;
  style?: IStyleConfig;
  error?: string;
  validation?: IValidationRule[];

  // Animation
  animate?: boolean;
  animationConfig?: IAnimationConfig;

  // Custom action function
  custom?: (field: IField, form: any) => void;
}

export interface IFieldEvents {
  // Focus events
  onFocus?: IEventHandler;
  onBlur?: IEventHandler;

  // Change events
  onChange?: IEventHandler;
  onInput?: IEventHandler;

  // Validation events
  onValidate?: IEventHandler;
  onValidationError?: IEventHandler;
  onValidationSuccess?: IEventHandler;

  // Mouse events
  onClick?: IEventHandler;
  onDoubleClick?: IEventHandler;
  onMouseEnter?: IEventHandler;
  onMouseLeave?: IEventHandler;

  // Keyboard events
  onKeyDown?: IEventHandler;
  onKeyUp?: IEventHandler;
  onKeyPress?: IEventHandler;
  onEnter?: IEventHandler;

  // File events
  onFileSelect?: IEventHandler;
  onFileRemove?: IEventHandler;
  onUploadStart?: IEventHandler;
  onUploadProgress?: IEventHandler;
  onUploadComplete?: IEventHandler;
  onUploadError?: IEventHandler;

  // Custom events
  [key: string]: IEventHandler | undefined;
}

export interface IEventHandler {
  action: string;
  params?: Record<string, any>;

  // Handler function
  handler?: (event: IFieldEvent) => void | Promise<void>;

  // Prevent default behavior
  preventDefault?: boolean;
  stopPropagation?: boolean;

  // Debouncing/Throttling
  debounce?: number;
  throttle?: number;
}

export interface IFieldEvent {
  type: string;
  field: IField;
  value: any;
  previousValue: any;
  form: any;
  originalEvent?: Event;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface IFieldDependency {
  field: string;
  type:
    | 'value'
    | 'visibility'
    | 'disabled'
    | 'required'
    | 'options'
    | 'validation';

  // Update strategy
  updateOn?: 'immediate' | 'blur' | 'change';

  // Transform dependency value
  transform?: (value: any) => any;

  // Debouncing
  debounce?: number;
}

export interface ITransformConfig {
  // Input transformation (before setting value)
  input?:
    | 'trim'
    | 'lowercase'
    | 'uppercase'
    | 'capitalize'
    | 'number'
    | 'boolean'
    | 'date'
    | 'json'
    | ((value: any) => any);

  // Output transformation (before getting value)
  output?:
    | 'trim'
    | 'lowercase'
    | 'uppercase'
    | 'capitalize'
    | 'number'
    | 'boolean'
    | 'date'
    | 'json'
    | 'stringify'
    | ((value: any) => any);

  // Display transformation (for UI only)
  display?: (value: any) => string;

  // Parse transformation (from display to value)
  parse?: (display: string) => any;
}

export interface IAccessibilityConfig {
  ariaLabel?: string;
  ariaLabelledBy?: string;
  ariaDescribedBy?: string;
  ariaRequired?: boolean;
  ariaInvalid?: boolean;
  ariaDisabled?: boolean;
  ariaReadOnly?: boolean;
  ariaHidden?: boolean;
  ariaExpanded?: boolean;
  ariaControls?: string;
  ariaLive?: 'off' | 'polite' | 'assertive';
  ariaAtomic?: boolean;
  ariaBusy?: boolean;
  ariaRelevant?: string;
  role?: string;
  tabIndex?: number;

  // Screen reader
  screenReaderOnly?: boolean;
  announceChanges?: boolean;
  announceErrors?: boolean;

  // Keyboard navigation
  keyboardShortcut?: string;
  skipNavigation?: boolean;
}

export interface IDateConfig {
  format?: string;
  displayFormat?: string;
  valueFormat?: string;

  minDate?: string | Date | 'today' | 'yesterday' | 'tomorrow';
  maxDate?: string | Date | 'today' | 'yesterday' | 'tomorrow';

  disabledDates?: (string | Date)[];
  disabledDays?: number[]; // 0-6 (Sunday to Saturday)
  enabledDates?: (string | Date)[];

  showTime?: boolean;
  showSeconds?: boolean;
  use24Hours?: boolean;
  timeStep?: {
    hour?: number;
    minute?: number;
    second?: number;
  };

  showWeekNumbers?: boolean;
  firstDayOfWeek?: number; // 0-6

  showYearDropdown?: boolean;
  showMonthDropdown?: boolean;
  yearRange?: [number, number];

  locale?: string;
  timezone?: string;

  showTodayButton?: boolean;
  showClearButton?: boolean;

  inline?: boolean;

  // Multiple dates
  multiple?: boolean;
  maxDates?: number;

  // Date range
  range?: boolean;
  minRange?: number;
  maxRange?: number;
}

export interface IFileConfig {
  accept?: string | string[];
  multiple?: boolean;

  minSize?: number;
  maxSize?: number;
  minFiles?: number;
  maxFiles?: number;

  // Image specific
  imagePreview?: boolean;
  imageCrop?: boolean;
  imageResize?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
  };
  aspectRatio?: number;

  // Upload configuration
  uploadUrl?: string;
  uploadMethod?: 'POST' | 'PUT';
  uploadHeaders?: Record<string, string>;
  uploadParams?: Record<string, any>;
  uploadFieldName?: string;

  // Upload behavior
  autoUpload?: boolean;
  sequential?: boolean;
  chunkSize?: number;

  // Progress & Status
  showProgress?: boolean;
  showFileList?: boolean;
  showPreview?: boolean;

  // Validation
  validateDimensions?: {
    minWidth?: number;
    maxWidth?: number;
    minHeight?: number;
    maxHeight?: number;
  };

  // Drag & Drop
  dragAndDrop?: boolean;
  dropzoneText?: string;

  // File processing
  processFile?: (file: File) => Promise<File | Blob>;

  // Events
  onBeforeUpload?: (file: File) => boolean | Promise<boolean>;
  onUploadProgress?: (progress: number, file: File) => void;
  onUploadSuccess?: (response: any, file: File) => void;
  onUploadError?: (error: any, file: File) => void;
  onFileRemove?: (file: File) => void;
}

export interface IEditorConfig {
  toolbar?:
    | boolean
    | string[]
    | {
        container: string[][];
        handlers?: Record<string, any>;
      };

  height?: string | number;
  minHeight?: string | number;
  maxHeight?: string | number;

  placeholder?: string;
  readOnly?: boolean;

  formats?: string[];

  // For rich text
  plugins?: string[];
  menubar?: boolean | string;
  statusbar?: boolean;

  // For code editor
  language?: string;
  theme?: string;
  lineNumbers?: boolean;
  lineWrapping?: boolean;
  autoCloseBrackets?: boolean;
  autoCloseTags?: boolean;
  matchBrackets?: boolean;
  tabSize?: number;
  indentWithTabs?: boolean;

  // For markdown
  preview?: boolean;
  previewStyle?: 'vertical' | 'horizontal' | 'tab';

  // Validation
  maxLength?: number;
  minLength?: number;

  // Auto-save
  autoSave?: boolean;
  autoSaveInterval?: number;
}

export interface IButtonConfig {
  text?: string;
  icon?: string;
  iconPosition?: 'left' | 'right';
  type?: 'button' | 'submit' | 'reset';
  variant?:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info'
    | 'light'
    | 'dark'
    | 'link';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  loadingText?: string;
  className?: string;
  style?: IStyleConfig;
  tooltip?: string;

  // Events
  onClick?: IEventHandler;

  // Conditional
  condition?: ICondition;
}

export interface ISectionValidation {
  validateOn?: 'blur' | 'change' | 'submit';
  showErrors?: boolean;
  scrollToError?: boolean;
  focusOnError?: boolean;

  // Custom validation
  validator?: (section: any) => boolean | string | Promise<boolean | string>;
}

export interface IStepValidation extends ISectionValidation {
  preventNext?: boolean;
  allowSkip?: boolean;

  // Completion requirements
  requiredFields?: string[];
  minCompletion?: number; // Percentage
}

export interface ICompletionCriteria {
  type: 'all' | 'any' | 'custom';
  fields?: string[];
  percentage?: number;
  custom?: (step: any) => boolean;
}

export interface IFormHooks {
  // Lifecycle hooks
  onInit?: (form: any) => void | Promise<void>;
  onMount?: (form: any) => void | Promise<void>;
  onDestroy?: (form: any) => void | Promise<void>;

  // Value hooks
  onValueChange?: (values: any, form: any) => void | Promise<void>;
  onFieldChange?: (
    field: string,
    value: any,
    form: any
  ) => void | Promise<void>;

  // Validation hooks
  beforeValidation?: (values: any, form: any) => any | Promise<any>;
  afterValidation?: (errors: any, form: any) => void | Promise<void>;

  // Submit hooks
  beforeSubmit?: (values: any, form: any) => any | Promise<any>;
  onSubmit?: (values: any, form: any) => void | Promise<void>;
  afterSubmit?: (response: any, form: any) => void | Promise<void>;
  onSubmitError?: (error: any, form: any) => void | Promise<void>;

  // Navigation hooks (for wizard)
  beforeStepChange?: (
    from: string,
    to: string,
    form: any
  ) => boolean | Promise<boolean>;
  afterStepChange?: (
    from: string,
    to: string,
    form: any
  ) => void | Promise<void>;

  // Error hooks
  onError?: (error: any, form: any) => void | Promise<void>;

  // State hooks
  onStateChange?: (state: any, form: any) => void | Promise<void>;
  onReset?: (form: any) => void | Promise<void>;
}

export interface IFormValidation {
  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'all';
  revalidateMode?: 'onChange' | 'onBlur' | 'onSubmit';

  showErrors?: boolean;
  scrollToError?: boolean;
  focusOnError?: boolean;

  // Error display
  errorDisplay?: 'inline' | 'tooltip' | 'summary' | 'none';
  errorPosition?: 'top' | 'bottom' | 'left' | 'right';

  // Custom validators
  validators?: Record<
    string,
    (form: any) => boolean | string | Promise<boolean | string>
  >;

  // Cross-field validation
  crossFieldValidation?: {
    fields: string[];
    validator: (values: any) => boolean | string | Promise<boolean | string>;
    message?: string;
  }[];
}

export interface IFormI18n {
  locale?: string;
  fallbackLocale?: string;

  messages?: Record<string, Record<string, string>>;

  dateLocale?: string;
  numberLocale?: string;

  rtl?: boolean;

  // Translation function
  translate?: (key: string, params?: Record<string, any>) => string;
}

// Form State Interfaces
export interface IFormState {
  values: Record<string, any>;
  errors: Record<string, string | string[]>;
  touched: Record<string, boolean>;
  dirty: Record<string, boolean>;
  disabled: Record<string, boolean>;
  hidden: Record<string, boolean>;

  isValid: boolean;
  isSubmitting: boolean;
  isValidating: boolean;
  submitCount: number;

  // For wizard
  currentStep?: string;
  completedSteps?: string[];
  visitedSteps?: string[];

  metadata?: Record<string, any>;
}

export interface IFormContext {
  form: IFormState;
  fields: Record<string, IField>;

  // Methods
  getValue: (path: string) => any;
  setValue: (path: string, value: any) => void;
  getError: (path: string) => string | string[] | undefined;
  setError: (path: string, error: string | string[]) => void;
  clearError: (path: string) => void;

  validate: (path?: string) => Promise<boolean>;
  validateField: (path: string) => Promise<boolean>;

  submit: () => Promise<void>;
  reset: (values?: any) => void;

  setFieldProperty: (path: string, property: string, value: any) => void;
  getFieldProperty: (path: string, property: string) => any;

  // For arrays
  addArrayItem: (path: string, value?: any, index?: number) => void;
  removeArrayItem: (path: string, index: number) => void;
  moveArrayItem: (path: string, from: number, to: number) => void;

  // For wizard
  nextStep?: () => void;
  previousStep?: () => void;
  goToStep?: (step: string) => void;

  // State management
  getState: () => IFormState;
  setState: (state: Partial<IFormState>) => void;
  subscribe: (callback: (state: IFormState) => void) => () => void;
}

// Complete Example with embedded fields
const employeeFormExample: IFormConfig = {
  id: 'employee-onboarding',
  version: '2.0.0',
  metadata: {
    title: 'Employee Onboarding',
    description: 'Complete employee onboarding form',
    estimatedTime: '15-20 minutes',
  },
  settings: {
    autoSave: true,
    autoSaveInterval: 30000,
    showProgressBar: true,
    validateOnBlur: true,
    scrollToError: true,
  },
  layout: {
    type: 'wizard',
    orientation: 'horizontal',
    steps: [
      {
        id: 'personal-info',
        title: 'Personal Information',
        icon: 'user',
        sections: [
          {
            id: 'basic-info',
            title: 'Basic Information',
            rows: [
              {
                id: 'name-row',
                columns: [
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'firstName',
                        name: 'firstName',
                        type: 'text',
                        label: 'First Name',
                        placeholder: 'Enter first name',
                        required: true,
                        validation: [
                          {
                            type: 'required',
                            message: 'First name is required',
                          },
                          { type: 'minLength', value: 2 },
                          { type: 'pattern', pattern: '^[a-zA-Z]+$' },
                        ],
                        transform: {
                          input: 'trim',
                          output: 'capitalize',
                        },
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'middleName',
                        name: 'middleName',
                        type: 'text',
                        label: 'Middle Name',
                        placeholder: 'Enter middle name',
                        required: false,
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'lastName',
                        name: 'lastName',
                        type: 'text',
                        label: 'Last Name',
                        placeholder: 'Enter last name',
                        required: true,
                        validation: [
                          {
                            type: 'required',
                            message: 'Last name is required',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                id: 'contact-row',
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email Address',
                        placeholder: '<EMAIL>',
                        required: true,
                        validation: [{ type: 'required' }, { type: 'email' }],
                        asyncValidation: [
                          {
                            type: 'unique',
                            endpoint: '/api/validate/email',
                            debounce: 500,
                            message: 'Email already exists',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'phone',
                        name: 'phone',
                        type: 'tel',
                        label: 'Phone Number',
                        placeholder: '+****************',
                        mask: '+****************',
                        required: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            id: 'address-section',
            title: 'Address Information',
            collapsible: true,
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'address',
                        name: 'address',
                        type: 'group',
                        label: 'Current Address',
                        groupConfig: {
                          bordered: true,
                          rows: [
                            {
                              columns: [
                                {
                                  width: 12,
                                  fields: [
                                    {
                                      id: 'street',
                                      name: 'street',
                                      type: 'text',
                                      label: 'Street Address',
                                      required: true,
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              columns: [
                                {
                                  width: { xs: 12, md: 6 },
                                  fields: [
                                    {
                                      id: 'city',
                                      name: 'city',
                                      type: 'text',
                                      label: 'City',
                                      required: true,
                                    },
                                  ],
                                },
                                {
                                  width: { xs: 12, md: 3 },
                                  fields: [
                                    {
                                      id: 'state',
                                      name: 'state',
                                      type: 'select',
                                      label: 'State',
                                      required: true,
                                      options: {
                                        type: 'api',
                                        url: '/api/states',
                                        valueField: 'code',
                                        labelField: 'name',
                                      },
                                    },
                                  ],
                                },
                                {
                                  width: { xs: 12, md: 3 },
                                  fields: [
                                    {
                                      id: 'zipCode',
                                      name: 'zipCode',
                                      type: 'text',
                                      label: 'ZIP Code',
                                      mask: '99999',
                                      required: true,
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'employment',
        title: 'Employment Details',
        icon: 'briefcase',
        sections: [
          {
            id: 'position-info',
            title: 'Position Information',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'department',
                        name: 'department',
                        type: 'select',
                        label: 'Department',
                        required: true,
                        options: {
                          type: 'api',
                          url: '/api/departments',
                          cache: true,
                        },
                        events: {
                          onChange: {
                            action: 'updateField',
                            target: 'position',
                            params: {
                              options: {
                                type: 'api',
                                url: '/api/positions',
                                params: { departmentId: '${value}' },
                              },
                            },
                          },
                        },
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'position',
                        name: 'position',
                        type: 'select',
                        label: 'Position',
                        required: true,
                        disabled: true,
                        condition: {
                          field: 'department',
                          operator: 'notEmpty',
                        },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'qualifications',
        title: 'Qualifications',
        icon: 'certificate',
        sections: [
          {
            id: 'education',
            title: 'Education',
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'education',
                        name: 'education',
                        type: 'array',
                        label: 'Educational Background',
                        arrayConfig: {
                          minItems: 1,
                          maxItems: 5,
                          addButton: {
                            text: 'Add Education',
                            icon: 'plus',
                          },
                          sortable: true,
                          collapsible: true,
                          template: {
                            rows: [
                              {
                                columns: [
                                  {
                                    width: { xs: 12, md: 6 },
                                    fields: [
                                      {
                                        id: 'degree',
                                        name: 'degree',
                                        type: 'select',
                                        label: 'Degree',
                                        required: true,
                                        options: [
                                          {
                                            value: 'bachelors',
                                            label: "Bachelor's",
                                          },
                                          {
                                            value: 'masters',
                                            label: "Master's",
                                          },
                                          { value: 'phd', label: 'PhD' },
                                        ],
                                      },
                                    ],
                                  },
                                  {
                                    width: { xs: 12, md: 6 },
                                    fields: [
                                      {
                                        id: 'institution',
                                        name: 'institution',
                                        type: 'text',
                                        label: 'Institution',
                                        required: true,
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                columns: [
                                  {
                                    width: { xs: 12, md: 4 },
                                    fields: [
                                      {
                                        id: 'fieldOfStudy',
                                        name: 'fieldOfStudy',
                                        type: 'text',
                                        label: 'Field of Study',
                                        required: true,
                                      },
                                    ],
                                  },
                                  {
                                    width: { xs: 12, md: 4 },
                                    fields: [
                                      {
                                        id: 'graduationYear',
                                        name: 'graduationYear',
                                        type: 'number',
                                        label: 'Graduation Year',
                                        min: 1950,
                                        max: 2030,
                                        required: true,
                                      },
                                    ],
                                  },
                                  {
                                    width: { xs: 12, md: 4 },
                                    fields: [
                                      {
                                        id: 'gpa',
                                        name: 'gpa',
                                        type: 'number',
                                        label: 'GPA',
                                        min: 0,
                                        max: 4.0,
                                        step: 0.1,
                                        precision: 2,
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                        },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  conditionals: [
    {
      id: 'show-permanent-address',
      condition: {
        field: 'sameAsCurrent',
        operator: 'equals',
        value: false,
      },
      actions: [
        {
          type: 'show',
          target: 'permanentAddress',
        },
      ],
    },
  ],
  hooks: {
    onInit: async (form) => {
      // Initialize form
    },
    beforeSubmit: async (values) => {
      // Transform values before submit
      return values;
    },
    afterSubmit: async (response) => {
      // Handle after submit
    },
    onError: (error) => {
      // Handle errors
    },
  },
};
