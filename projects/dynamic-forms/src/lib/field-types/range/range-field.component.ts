import { Component, OnInit } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-range-field',
  template: `
    <div class="form-field" [formGroup]="form" [ngClass]="field.cssClass ?? ''">
      <label [for]="field.key" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>
      
      <div class="range-wrapper">
        <span class="range-min" *ngIf="showMinMax">{{ min }}</span>
        <input
          [id]="field.key"
          type="range"
          [formControlName]="field.key"
          [readonly]="field.readonly"
          [min]="min"
          [max]="max"
          [step]="step"
          class="range-input"
          [class.is-invalid]="isInvalid"
        />
        <span class="range-max" *ngIf="showMinMax">{{ max }}</span>
      </div>
      
      <div class="range-value" *ngIf="showValue">
        Current value: <strong>{{ control?.value || min }}</strong>
      </div>
      
      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>
      
      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [`
    .form-field {
      margin-bottom: 1rem;
    }
    
    label {
      display: block;
      margin-bottom: 0.25rem;
      font-weight: 500;
    }
    
    .required {
      color: red;
    }
    
    .range-wrapper {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.5rem;
    }
    
    .range-input {
      flex: 1;
      height: 6px;
      border-radius: 3px;
      background: #ddd;
      outline: none;
      -webkit-appearance: none;
    }
    
    .range-input::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #007bff;
      cursor: pointer;
      border: 2px solid #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .range-input::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #007bff;
      cursor: pointer;
      border: 2px solid #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .range-input:focus {
      background: #bbb;
    }
    
    .range-input.is-invalid {
      background: #f8d7da;
    }
    
    .range-min,
    .range-max {
      font-size: 0.875rem;
      color: #6c757d;
      min-width: 2rem;
      text-align: center;
    }
    
    .range-value {
      text-align: center;
      font-size: 0.875rem;
      color: #495057;
      margin-bottom: 0.5rem;
    }
    
    .hint {
      display: block;
      margin-top: 0.25rem;
      color: #6c757d;
      font-size: 0.875rem;
    }
    
    .error-messages {
      margin-top: 0.25rem;
    }
    
    .error {
      display: block;
      color: #dc3545;
      font-size: 0.875rem;
    }
  `]
})
export class RangeFieldComponent extends BaseFieldComponent implements OnInit {
  public min: number = 0;
  public max: number = 100;
  public step: number = 1;
  public showMinMax: boolean = true;
  public showValue: boolean = true;

  public ngOnInit(): void {
    super.ngOnInit();
    const fieldConfig = this.field as any;
    this.min = fieldConfig.min ?? 0;
    this.max = fieldConfig.max ?? 100;
    this.step = fieldConfig.step ?? 1;
    this.showMinMax = fieldConfig.showMinMax ?? true;
    this.showValue = fieldConfig.showValue ?? true;
  }
}
