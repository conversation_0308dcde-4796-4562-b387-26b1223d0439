import { Component, OnInit } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-time-field',
  template: `
    <div class="form-field" [formGroup]="form" [ngClass]="field.cssClass ?? ''">
      <label [for]="field.key" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>
      
      <input
        [id]="field.key"
        [type]="inputType"
        [formControlName]="field.key"
        [placeholder]="field.placeholder || ''"
        [readonly]="field.readonly"
        [min]="minTime"
        [max]="maxTime"
        [step]="step"
        class="form-control"
        [class.is-invalid]="isInvalid"
      />
      
      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>
      
      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [`
    .form-field {
      margin-bottom: 1rem;
    }
    
    label {
      display: block;
      margin-bottom: 0.25rem;
      font-weight: 500;
    }
    
    .required {
      color: red;
    }
    
    .form-control {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
    }
    
    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
    
    .form-control.is-invalid {
      border-color: #dc3545;
    }
    
    .hint {
      display: block;
      margin-top: 0.25rem;
      color: #6c757d;
      font-size: 0.875rem;
    }
    
    .error-messages {
      margin-top: 0.25rem;
    }
    
    .error {
      display: block;
      color: #dc3545;
      font-size: 0.875rem;
    }
  `]
})
export class TimeFieldComponent extends BaseFieldComponent implements OnInit {
  public inputType: string = 'time';
  public minTime?: string;
  public maxTime?: string;
  public step?: number;

  public ngOnInit(): void {
    super.ngOnInit();
    const fieldConfig = this.field as any;
    
    // Support different time input types
    if (fieldConfig.timeType) {
      this.inputType = fieldConfig.timeType; // time, datetime-local, month, week
    }
    
    this.minTime = fieldConfig.minTime;
    this.maxTime = fieldConfig.maxTime;
    this.step = fieldConfig.step;
  }
}
