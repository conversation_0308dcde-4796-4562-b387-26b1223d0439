import { Component } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-color-field',
  template: `
    <div class="form-field" [formGroup]="form" [ngClass]="field.cssClass ?? ''">
      <label [for]="field.key" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>
      
      <div class="color-input-wrapper">
        <input
          [id]="field.key"
          type="color"
          [formControlName]="field.key"
          [readonly]="field.readonly"
          class="color-input"
          [class.is-invalid]="isInvalid"
        />
        <input
          type="text"
          [formControlName]="field.key"
          [placeholder]="field.placeholder || '#000000'"
          [readonly]="field.readonly"
          class="color-text-input form-control"
          [class.is-invalid]="isInvalid"
        />
      </div>
      
      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>
      
      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [`
    .form-field {
      margin-bottom: 1rem;
    }
    
    label {
      display: block;
      margin-bottom: 0.25rem;
      font-weight: 500;
    }
    
    .required {
      color: red;
    }
    
    .color-input-wrapper {
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }
    
    .color-input {
      width: 50px;
      height: 40px;
      border: 1px solid #ccc;
      border-radius: 4px;
      cursor: pointer;
      padding: 0;
    }
    
    .color-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
    
    .color-input.is-invalid {
      border-color: #dc3545;
    }
    
    .color-text-input {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
      font-family: monospace;
    }
    
    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
    
    .form-control.is-invalid {
      border-color: #dc3545;
    }
    
    .hint {
      display: block;
      margin-top: 0.25rem;
      color: #6c757d;
      font-size: 0.875rem;
    }
    
    .error-messages {
      margin-top: 0.25rem;
    }
    
    .error {
      display: block;
      color: #dc3545;
      font-size: 0.875rem;
    }
  `]
})
export class ColorFieldComponent extends BaseFieldComponent {}
