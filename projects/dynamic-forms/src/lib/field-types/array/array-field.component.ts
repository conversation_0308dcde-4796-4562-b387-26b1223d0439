import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BaseFieldComponent } from '../base/base-field.component';
import { FieldConfig } from '../../core/models/field-config.interface';
import { FormBuilderService } from '../../core/services/form-builder.service';

interface ArrayItemData {
  fieldConfig: FieldConfig;
  formGroup: FormGroup;
  isInvalid: boolean;
}

@Component({
  selector: 'lib-array-field',
  template: `
    <div
      class="form-field array-field"
      [formGroup]="form"
      [ngClass]="field.cssClass ?? ''"
    >
      <label class="field-label" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>

      <div class="array-container" [formArrayName]="field.key">
        <div class="array-items" *ngIf="arrayItems.length > 0">
          <div
            class="array-item"
            *ngFor="
              let arrayItem of arrayItems;
              let i = index;
              trackBy: trackByIndex
            "
            [class.has-error]="arrayItem.isInvalid"
          >
            <div class="item-header">
              <span class="item-number">{{ i + 1 }}.</span>
              <button
                type="button"
                class="btn-remove"
                (click)="removeItem(i)"
                [disabled]="formArray.length <= minItems"
                [attr.aria-label]="'Remove item ' + (i + 1)"
              >
                ✕
              </button>
            </div>

            <div class="item-content">
              <lib-dynamic-field
                [field]="arrayItem.fieldConfig"
                [form]="arrayItem.formGroup"
              ></lib-dynamic-field>
            </div>
          </div>
        </div>

        <div class="empty-state" *ngIf="formArray.length === 0">
          <p class="empty-message">{{ emptyMessage }}</p>
        </div>

        <div class="array-actions">
          <button
            type="button"
            class="btn-add"
            (click)="addItem()"
            [disabled]="formArray.length >= maxItems"
          >
            <span class="add-icon">+</span>
            {{ addButtonText }}
          </button>

          <div class="array-info" *ngIf="showItemCount">
            <small class="item-count">
              {{ formArray.length }} of {{ maxItemsDisplay }} items
            </small>
          </div>
        </div>
      </div>

      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>

      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [
    `
      .array-field {
        margin-bottom: 1.5rem;
      }

      .field-label {
        display: block;
        margin-bottom: 0.75rem;
        font-weight: 500;
        font-size: 1rem;
      }

      .required {
        color: #dc3545;
      }

      .array-container {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 1rem;
        background-color: #f8f9fa;
      }

      .array-items {
        margin-bottom: 1rem;
      }

      .array-item {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        margin-bottom: 0.75rem;
        overflow: hidden;
        transition: border-color 0.2s ease;
      }

      .array-item.has-error {
        border-color: #dc3545;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0.75rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
      }

      .item-number {
        font-weight: 500;
        color: #495057;
        font-size: 0.875rem;
      }

      .btn-remove {
        background: none;
        border: none;
        color: #dc3545;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 3px;
        font-size: 1rem;
        line-height: 1;
        transition: background-color 0.2s ease;
      }

      .btn-remove:hover:not(:disabled) {
        background-color: #dc3545;
        color: white;
      }

      .btn-remove:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .item-content {
        padding: 1rem 0.75rem;
      }

      .empty-state {
        text-align: center;
        padding: 2rem 1rem;
        color: #6c757d;
      }

      .empty-message {
        margin: 0;
        font-style: italic;
      }

      .array-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
      }

      .btn-add {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background-color: #007bff;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: background-color 0.2s ease;
      }

      .btn-add:hover:not(:disabled) {
        background-color: #0056b3;
      }

      .btn-add:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }

      .add-icon {
        font-size: 1rem;
        font-weight: bold;
      }

      .array-info {
        flex: 1;
        text-align: right;
      }

      .item-count {
        color: #6c757d;
        font-size: 0.75rem;
      }

      .hint {
        display: block;
        margin-top: 0.5rem;
        color: #6c757d;
        font-size: 0.875rem;
      }

      .error-messages {
        margin-top: 0.5rem;
      }

      .error {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
      }
    `,
  ],
})
export class ArrayFieldComponent
  extends BaseFieldComponent
  implements OnInit, OnDestroy
{
  public formArray!: FormArray;
  public arrayItems: ArrayItemData[] = [];
  public minItems: number = 0;
  public maxItems: number = Infinity;
  public maxItemsDisplay: string = '∞';
  public addButtonText: string = 'Add Item';
  public emptyMessage: string = 'No items added yet';
  public showItemCount: boolean = true;

  private readonly subscriptions: Subscription[] = [];

  constructor(private readonly formBuilderService: FormBuilderService) {
    super();
  }

  public ngOnInit(): void {
    super.ngOnInit();

    // Get the FormArray from the parent form
    const arrayControl = this.form.get(this.field.key);
    if (arrayControl instanceof FormArray) {
      this.formArray = arrayControl;
    } else {
      console.error(`Field "${this.field.key}" is not a FormArray`);
      return;
    }

    // Configure array settings from field config
    const fieldConfig = this.field as any;
    this.minItems = fieldConfig.minItems ?? 0;
    this.maxItems = fieldConfig.maxItems ?? Infinity;
    this.maxItemsDisplay =
      this.maxItems === Infinity ? '∞' : this.maxItems.toString();
    this.addButtonText = fieldConfig.addButtonText ?? 'Add Item';
    this.emptyMessage = fieldConfig.emptyMessage ?? 'No items added yet';
    this.showItemCount = fieldConfig.showItemCount ?? true;

    // Initialize with minimum items if specified
    if (this.formArray.length < this.minItems) {
      for (let i = this.formArray.length; i < this.minItems; i++) {
        this.addItem();
      }
    }

    // Build initial array items data
    this.buildArrayItems();

    // Subscribe to form array changes to rebuild array items
    this.subscriptions.push(
      this.formArray.valueChanges.subscribe(() => {
        this.buildArrayItems();
      })
    );

    this.subscriptions.push(
      this.formArray.statusChanges.subscribe(() => {
        this.buildArrayItems();
      })
    );
  }

  public ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  public buildArrayItems(): void {
    this.arrayItems = [];

    for (let i = 0; i < this.formArray.length; i++) {
      const control = this.formArray.at(i);
      const fieldConfig = this.getItemFieldConfig(i);
      const formGroup = this.getItemFormGroup(i);
      const isInvalid = control ? control.invalid && control.touched : false;

      this.arrayItems.push({
        fieldConfig,
        formGroup,
        isInvalid,
      });
    }
  }

  public trackByIndex(index: number): number {
    return index;
  }

  public addItem(): void {
    if (!this.field.fieldArray || this.formArray.length >= this.maxItems) {
      return;
    }

    this.formBuilderService.addArrayItem(this.formArray, this.field.fieldArray);
    // buildArrayItems will be called automatically via subscription
  }

  public removeItem(index: number): void {
    if (this.formArray.length <= this.minItems) {
      return;
    }

    this.formBuilderService.removeArrayItem(this.formArray, index);
    // buildArrayItems will be called automatically via subscription
  }

  public getItemFieldConfig(index: number): FieldConfig {
    if (!this.field.fieldArray) {
      throw new Error('fieldArray configuration is required for array fields');
    }

    // Create a unique key for each array item
    return {
      ...this.field.fieldArray,
      key: `${index}`, // Use index as key for the array item
    };
  }

  public getItemFormGroup(index: number): FormGroup {
    const control = this.formArray.at(index);
    if (control instanceof FormGroup) {
      return control;
    }

    // For simple controls, create a wrapper FormGroup
    const wrapperGroup = new FormGroup({});
    wrapperGroup.addControl(this.field.fieldArray?.key || 'value', control);
    return wrapperGroup;
  }
}
