import { Component } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-password-field',
  template: `
    <div class="form-field" [formGroup]="form" [ngClass]="field.cssClass ?? ''">
      <label [for]="field.key" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>

      <div class="password-input-wrapper">
        <input
          [id]="field.key"
          [type]="showPassword ? 'text' : 'password'"
          [formControlName]="field.key"
          [placeholder]="field.placeholder || ''"
          [readonly]="field.readonly"
          class="form-control"
          [class.is-invalid]="isInvalid"
          autocomplete="current-password"
        />
        <button
          type="button"
          class="toggle-password"
          (click)="togglePasswordVisibility()"
          [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
          tabindex="-1"
        >
          <span class="eye-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</span>
        </button>
      </div>

      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>

      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [
    `
      .form-field {
        margin-bottom: 1rem;
      }

      label {
        display: block;
        margin-bottom: 0.25rem;
        font-weight: 500;
      }

      .required {
        color: red;
      }

      .password-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
      }

      .form-control {
        width: 100%;
        padding: 0.5rem;
        padding-right: 2.5rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 1rem;
      }

      .form-control:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      .form-control.is-invalid {
        border-color: #dc3545;
      }

      .toggle-password {
        position: absolute;
        right: 0.5rem;
        background: none;
        border: none;
        padding: 0.25rem;
        cursor: pointer;
        font-size: 1.2rem;
        opacity: 0.7;
        transition: opacity 0.2s;
      }

      .toggle-password:hover {
        opacity: 1;
      }

      .eye-icon {
        display: block;
        width: 24px;
        height: 24px;
        line-height: 24px;
      }

      .hint {
        display: block;
        margin-top: 0.25rem;
        color: #6c757d;
        font-size: 0.875rem;
      }

      .error-messages {
        margin-top: 0.25rem;
      }

      .error {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
      }
    `,
  ],
})
export class PasswordFieldComponent extends BaseFieldComponent {
  public showPassword: boolean = false;

  public togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
