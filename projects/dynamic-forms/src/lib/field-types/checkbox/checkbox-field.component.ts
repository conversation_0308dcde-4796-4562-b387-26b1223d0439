import { Component } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-checkbox-field',
  template: `
    <div
      class="form-field checkbox-field"
      [formGroup]="form"
      [ngClass]="field.cssClass ?? ''"
    >
      <label class="checkbox-label">
        <input type="checkbox" [formControlName]="field.key" [id]="field.key" />
        <span class="label-text">
          {{ field.label }}
          <span class="required" *ngIf="field.required">*</span>
        </span>
      </label>

      <small class="hint" *ngIf="field.hint">{{ field.hint }}</small>

      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [
    `
      .checkbox-field {
        margin-bottom: 1rem;
      }

      .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      input[type='checkbox'] {
        margin-right: 0.5rem;
        width: 18px;
        height: 18px;
        cursor: pointer;
      }

      .label-text {
        font-weight: 400;
        user-select: none;
      }

      .required {
        color: red;
      }

      .hint {
        display: block;
        margin-top: 0.25rem;
        margin-left: 26px;
        color: #6c757d;
        font-size: 0.875rem;
      }

      .error-messages {
        margin-top: 0.25rem;
        margin-left: 26px;
      }

      .error {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
      }
    `,
  ],
})
export class CheckboxFieldComponent extends BaseFieldComponent {}
