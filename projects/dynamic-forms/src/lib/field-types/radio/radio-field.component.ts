import { Component } from '@angular/core';
import { BaseFieldComponent } from '../base/base-field.component';

@Component({
  selector: 'lib-radio-field',
  template: `
    <div class="form-field" [formGroup]="form" [ngClass]="field.cssClass ?? ''">
      <label class="field-label" *ngIf="field.label">
        {{ field.label }}
        <span class="required" *ngIf="field.required">*</span>
      </label>

      <div class="radio-group">
        <label
          class="radio-label"
          *ngFor="let option of field.options"
          [class.disabled]="option.disabled"
        >
          <input
            type="radio"
            [formControlName]="field.key"
            [value]="option.value"
            class="radio-input"
            [attr.disabled]="option.disabled ? '' : null"
          />
          <span class="radio-text">{{ option.label }}</span>
        </label>
      </div>

      <small class="hint" *ngIf="field.hint && !isInvalid">
        {{ field.hint }}
      </small>

      <div class="error-messages" *ngIf="isInvalid">
        <small class="error" *ngFor="let error of errors">{{ error }}</small>
      </div>
    </div>
  `,
  styles: [
    `
      .form-field {
        margin-bottom: 1rem;
      }

      .field-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
      }

      .required {
        color: red;
      }

      .radio-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .radio-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0.25rem 0;
      }

      .radio-label.disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .radio-input {
        margin-right: 0.5rem;
        width: 18px;
        height: 18px;
        cursor: pointer;
      }

      .radio-label.disabled .radio-input {
        cursor: not-allowed;
      }

      .radio-text {
        user-select: none;
      }

      .hint {
        display: block;
        margin-top: 0.5rem;
        color: #6c757d;
        font-size: 0.875rem;
      }

      .error-messages {
        margin-top: 0.25rem;
      }

      .error {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
      }
    `,
  ],
})
export class RadioFieldComponent extends BaseFieldComponent {}
