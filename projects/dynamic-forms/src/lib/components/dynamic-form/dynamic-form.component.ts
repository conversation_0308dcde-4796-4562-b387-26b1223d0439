// dynamic-form.component.ts
import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormBuilderService } from '../../core/services/form-builder.service';
import { DynamicFormConfig } from '../../core/models/dynamic-form-config.interface';

@Component({
  selector: 'lib-dynamic-form',
  template: `
    <form
      *ngIf="form"
      [formGroup]="form"
      (ngSubmit)="onSubmit()"
      [ngClass]="config.cssClass ?? ''"
    >
      <lib-dynamic-field
        *ngFor="let field of config.fields"
        [field]="field"
        [form]="form"
      ></lib-dynamic-field>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary" [disabled]="!form.valid">
          {{ config.submitLabel || 'Submit' }}
        </button>

        <button
          type="button"
          class="btn btn-secondary"
          (click)="onReset()"
          *ngIf="config.resetLabel !== ''"
        >
          {{ config.resetLabel || 'Reset' }}
        </button>
      </div>
    </form>
  `,
  styles: [
    `
      form {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem;
      }

      .form-actions {
        margin-top: 2rem;
        display: flex;
        gap: 1rem;
      }

      .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .btn-primary {
        background-color: #007bff;
        color: white;
      }

      .btn-primary:hover:not(:disabled) {
        background-color: #0056b3;
      }

      .btn-primary:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
        opacity: 0.65;
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background-color: #545b62;
      }
    `,
  ],
})
export class DynamicFormComponent implements OnInit {
  @Input() public config!: DynamicFormConfig;
  @Input() public initialValues?: any;

  @Output() public formSubmit = new EventEmitter<any>();
  @Output() public formChange = new EventEmitter<any>();
  @Output() public formReset = new EventEmitter<void>();

  public form!: FormGroup;

  public constructor(private readonly formBuilder: FormBuilderService) {}

  public ngOnInit(): void {
    this.form = this.formBuilder.createForm(this.config.fields);

    // Set initial values if provided
    if (this.initialValues) {
      this.form.patchValue(this.initialValues);
    }

    // Emit form changes
    this.form.valueChanges.subscribe((value) => {
      this.formChange.emit({
        value,
        valid: this.form.valid,
        errors: this.form.errors,
      });
    });
  }

  public onSubmit(): void {
    if (this.form.valid) {
      this.formSubmit.emit(this.form.value);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.form.controls).forEach((key) => {
        const control = this.form.get(key);
        control?.markAsTouched();
      });
    }
  }

  public onReset(): void {
    this.formBuilder.resetForm(this.form);
    this.formReset.emit();
  }

  public getValue(): any {
    return this.form.value;
  }

  public setValue(value: any): void {
    this.form.setValue(value);
  }

  public patchValue(value: any): void {
    this.form.patchValue(value);
  }

  public isValid(): boolean {
    return this.form.valid;
  }
}
