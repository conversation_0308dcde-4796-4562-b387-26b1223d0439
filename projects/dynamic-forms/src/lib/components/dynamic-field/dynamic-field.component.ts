import {
  Component,
  Input,
  OnInit,
  ViewChild,
  ViewContainerRef,
  ComponentRef,
  Type,
  ComponentFactoryResolver,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FieldConfig } from '../../core/models/field-config.interface';
import { FieldType } from '../../core/models/field-type.enum';
import { InputFieldComponent } from '../../field-types/input/input-field.component';
import { SelectFieldComponent } from '../../field-types/select/select-field.component';
import { CheckboxFieldComponent } from '../../field-types/checkbox/checkbox-field.component';
import { EmailFieldComponent } from '../../field-types/email/email-field.component';
import { PasswordFieldComponent } from '../../field-types/password/password-field.component';
import { NumberFieldComponent } from '../../field-types/number/number-field.component';
import { DateFieldComponent } from '../../field-types/date/date-field.component';
import { TextareaFieldComponent } from '../../field-types/textarea/textarea-field.component';
import { RadioFieldComponent } from '../../field-types/radio/radio-field.component';
import { UrlFieldComponent } from '../../field-types/url/url-field.component';
import { TelFieldComponent } from '../../field-types/tel/tel-field.component';
import { SearchFieldComponent } from '../../field-types/search/search-field.component';
import { ColorFieldComponent } from '../../field-types/color/color-field.component';
import { RangeFieldComponent } from '../../field-types/range/range-field.component';
import { TimeFieldComponent } from '../../field-types/time/time-field.component';
import { FileFieldComponent } from '../../field-types/file/file-field.component';
import { ArrayFieldComponent } from '../../field-types/array/array-field.component';
import { BaseFieldComponent } from '../../field-types/base/base-field.component';

@Component({
  selector: 'lib-dynamic-field',
  template: `
    <ng-container #fieldContainer></ng-container>
    <div *ngIf="showGroupField" class="form-group" [formGroup]="form">
      <fieldset>
        <legend *ngIf="field.label">{{ field.label }}</legend>
        <div [formGroupName]="field.key">
          <lib-dynamic-field
            *ngFor="let childField of field.fieldGroup"
            [field]="childField"
            [form]="nestedFormGroup"
          ></lib-dynamic-field>
        </div>
      </fieldset>
    </div>
  `,
  styles: [
    `
      .form-group {
        margin-bottom: 1rem;
      }

      fieldset {
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 1rem;
        margin-bottom: 1rem;
      }

      legend {
        font-weight: 600;
        padding: 0 0.5rem;
        width: auto;
      }
    `,
  ],
})
export class DynamicFieldComponent implements OnInit {
  @Input() public field!: FieldConfig;
  @Input() public form!: FormGroup;

  @ViewChild('fieldContainer', { read: ViewContainerRef, static: true })
  public fieldContainer!: ViewContainerRef;

  private componentRef!: ComponentRef<BaseFieldComponent>;

  // Pre-computed properties for template
  public showGroupField: boolean = false;
  public nestedFormGroup!: FormGroup;

  public constructor(
    private readonly componentFactoryResolver: ComponentFactoryResolver
  ) {}

  private readonly fieldComponents: {
    [key: string]: Type<BaseFieldComponent>;
  } = {
    // Basic input types
    [FieldType.INPUT]: InputFieldComponent,
    [FieldType.TEXT]: InputFieldComponent,
    [FieldType.EMAIL]: EmailFieldComponent,
    [FieldType.PASSWORD]: PasswordFieldComponent,
    [FieldType.NUMBER]: NumberFieldComponent,

    // URL and communication
    [FieldType.URL]: UrlFieldComponent,
    [FieldType.TEL]: TelFieldComponent,

    // Date and time types
    [FieldType.DATE]: DateFieldComponent,
    [FieldType.TIME]: TimeFieldComponent,
    [FieldType.DATETIME_LOCAL]: TimeFieldComponent,
    [FieldType.MONTH]: TimeFieldComponent,
    [FieldType.WEEK]: TimeFieldComponent,

    // Selection types
    [FieldType.SELECT]: SelectFieldComponent,
    [FieldType.RADIO]: RadioFieldComponent,
    [FieldType.CHECKBOX]: CheckboxFieldComponent,

    // Text areas
    [FieldType.TEXTAREA]: TextareaFieldComponent,

    // Special input types
    [FieldType.SEARCH]: SearchFieldComponent,
    [FieldType.COLOR]: ColorFieldComponent,
    [FieldType.RANGE]: RangeFieldComponent,
    [FieldType.FILE]: FileFieldComponent,

    // Complex types
    [FieldType.ARRAY]: ArrayFieldComponent,
  };

  public ngOnInit(): void {
    // Pre-compute template variables
    this.showGroupField =
      !this.field.hidden &&
      this.field.type === FieldType.GROUP &&
      !!this.field.fieldGroup;

    // Set nested form group if it's a group type
    if (this.field.type === FieldType.GROUP) {
      const nestedGroup = this.form.get(this.field.key);
      if (nestedGroup instanceof FormGroup) {
        this.nestedFormGroup = nestedGroup;
      }
    } else {
      this.nestedFormGroup = this.form;
    }

    // Create regular field component
    if (
      !this.field.hidden &&
      this.field.type !== FieldType.GROUP &&
      this.field.type !== FieldType.ARRAY
    ) {
      this.createFieldComponent();
    }

    // Handle ARRAY fields separately (they need special handling like GROUP)
    if (!this.field.hidden && this.field.type === FieldType.ARRAY) {
      this.createFieldComponent();
    }
  }

  private createFieldComponent(): void {
    const componentType = this.fieldComponents[this.field.type];

    if (!componentType) {
      console.error(`Field type "${this.field.type}" is not supported`);
      return;
    }

    this.fieldContainer.clear();

    const componentFactory =
      this.componentFactoryResolver.resolveComponentFactory(componentType);
    this.componentRef = this.fieldContainer.createComponent(componentFactory);
    this.componentRef.instance.field = this.field;
    this.componentRef.instance.form = this.form;
  }
}
