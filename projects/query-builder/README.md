# @ng-omar/query-builder

A powerful TypeScript query builder library for Angular applications with fluent API support for dynamic filtering, sorting, and pagination.

## Features

- 🔍 **Advanced Filtering**: Complex nested filters with AND/OR/NOT logic
- 📄 **Flexible Pagination**: Support for both offset-based and cursor-based pagination
- 🔀 **Dynamic Sorting**: Multi-field sorting with null handling
- 🎯 **Type Safety**: Full TypeScript support with generic types
- 🏗️ **Fluent API**: Intuitive builder pattern for query construction
- ✅ **Validation**: Built-in validation with detailed error reporting
- 🚀 **Performance**: Optimized for large datasets with caching support
- 📊 **Export Ready**: Built-in support for CSV, Excel, JSON, XML formats

## Installation

```bash
npm install @ng-omar/query-builder
```

## Quick Start

### 1. Import the Module

```typescript
import { QueryBuilderModule } from "@ng-omar/query-builder";

@NgModule({
  imports: [QueryBuilderModule],
})
export class AppModule {}
```

### 2. Inject the Service

```typescript
import { QueryBuilderService } from '@ng-omar/query-builder';

@Component({...})
export class MyComponent {
  constructor(private queryBuilder: QueryBuilderService) {}
}
```

### 3. Build Queries

```typescript
// Simple list query
const listQuery = this.queryBuilder.quickList(1, 20);

// Advanced filtered query
const complexQuery = this.queryBuilder
  .createListRequest<User>()
  .filter((filter) => filter.and((and) => and.eq("status", "active").gt("age", 18)))
  .search("john")
  .sortDesc("createdAt")
  .paginate(1, 10)
  .build();
```

## API Reference

### Filter Operators

```typescript
// Equality
filter.eq("name", "John"); // name = 'John'
filter.ne("status", "inactive"); // name != 'inactive'

// Comparison
filter.gt("age", 18); // age > 18
filter.gte("score", 80); // score >= 80
filter.lt("price", 100); // price < 100
filter.lte("discount", 50); // discount <= 50

// String Operations
filter.like("name", "%john%"); // name LIKE '%john%'
filter.ilike("email", "%GMAIL%"); // email ILIKE '%GMAIL%'
filter.contains("title", "angular"); // title CONTAINS 'angular'
filter.startsWith("code", "NG"); // code STARTS WITH 'NG'
filter.endsWith("file", ".ts"); // file ENDS WITH '.ts'

// Array Operations
filter.in("category", ["tech", "science"]); // category IN (...)
filter.notIn("status", ["deleted", "banned"]); // category NOT IN (...)

// Range Operations
filter.between("date", startDate, endDate); // date BETWEEN start AND end

// Null Operations
filter.isNull("deletedAt"); // deletedAt IS NULL
filter.isNotNull("createdAt"); // createdAt IS NOT NULL
filter.isEmpty("description"); // description IS EMPTY
filter.isNotEmpty("content"); // content IS NOT EMPTY

// Pattern Operations
filter.regex("phone", "^\\+1"); // phone REGEX '^\\+1'
```

### Complex Filtering

```typescript
// Nested AND/OR logic
const query = this.queryBuilder
  .createListRequest<Product>()
  .filter((filter) =>
    filter.and((and) =>
      and
        .eq("category", "electronics")
        .or((or) => or.between("price", 100, 500).eq("onSale", true))
        .not((not) => not.eq("status", "discontinued"))
    )
  )
  .build();
```

### Sorting

```typescript
// Single field sorting
const query = this.queryBuilder.createListRequest<User>().sortAsc("name").sortDesc("createdAt").build();

// Using sort builder
const sorts = this.queryBuilder.createMultipleSort<User>((sort) => sort.asc("category").desc("price").asc("name"));
```

### Pagination

```typescript
// Offset-based pagination
const query = this.queryBuilder
  .createListRequest<Product>()
  .paginate(2, 50) // page 2, 50 items per page
  .build();

// Cursor-based pagination
const query = this.queryBuilder.createListRequest<Product>().paginateWithCursor("eyJpZCI6MTIzfQ==", 25).build();
```

### Field Selection

```typescript
// Select specific fields
const query = this.queryBuilder.createOneRequest<User>().select("id", "name", "email", "createdAt").build();

// Select all fields (default)
const query = this.queryBuilder.createOneRequest<User>().selectAll().build();
```

### Export Queries

```typescript
// CSV export
const exportQuery = this.queryBuilder.createExportRequest<Product>(
  ResponseFormatType.Csv,
  5000 // max records
);

// Excel export with custom formatting
const excelQuery = this.queryBuilder
  .createListRequest<Product>()
  .format({
    type: ResponseFormatType.Excel,
    includeNullFields: true,
    flattenNested: true,
  })
  .build();
```

### CRUD Operations

```typescript
// Count records
const countQuery = this.queryBuilder.createCountRequest({
  active: { filters: this.queryBuilder.createEqFilter("status", "active") },
  inactive: { filters: this.queryBuilder.createEqFilter("status", "inactive") },
});

// Check existence
const existsQuery = this.queryBuilder.createExistsRequest({
  hasEmail: { filters: this.queryBuilder.createFilter().isNotNull("email").build() },
});

// Update records
const updateQuery = this.queryBuilder.createUpdateByIdRequest(123, {
  status: "active",
  updatedAt: new Date(),
});

// Delete records (soft delete by default)
const deleteQuery = this.queryBuilder.createDeleteByIdRequest(123);

// Hard delete
const hardDeleteQuery = this.queryBuilder.createDeleteByIdRequest(123, true);

// Restore deleted records
const restoreQuery = this.queryBuilder.createRestoreByIdRequest(123);
```

## Validation

The library includes comprehensive validation:

```typescript
import { ValidationUtils } from "@ng-omar/query-builder";

// Validate any request
const validation = ValidationUtils.validateListRequest(query);

if (!validation.valid) {
  console.log("Errors:", validation.errors);
  console.log("Warnings:", validation.warnings);

  // Get recommendations
  const recommendations = ValidationUtils.getRecommendations(validation);
  console.log("Suggestions:", recommendations);
}
```

## Utilities

```typescript
import { QueryUtils } from "@ng-omar/query-builder";

// Analyze queries
const conditionCount = QueryUtils.countConditions(filterGroup);
const maxDepth = QueryUtils.getMaxDepth(filterGroup);
const fields = QueryUtils.extractFields(filterGroup);

// Handle pagination
const offset = QueryUtils.calculateOffset(pageNumber, pageSize);
const totalPages = QueryUtils.calculateTotalPages(totalRecords, pageSize);

// Parse field selections
const fields = QueryUtils.parseSelectFields("id,name,email");
```

## Type Safety

Full TypeScript support with generics:

```typescript
interface User {
  id: number;
  name: string;
  email: string;
  age: number;
  createdAt: Date;
}

// Type-safe query building
const query = this.queryBuilder
  .createListRequest<User>()
  .filter(
    (filter) =>
      filter
        .eq("name", "John") // ✅ Valid
        .gt("age", 25) // ✅ Valid
        .eq("invalid", "test") // ❌ TypeScript error
  )
  .select("id", "name", "email") // ✅ Only User keys allowed
  .sortAsc("createdAt") // ✅ Type-safe sorting
  .build();
```

## Performance Tips

- Use pagination for large datasets
- Limit the number of sort fields (< 5 recommended)
- Keep filter nesting depth reasonable (< 5 levels)
- Use specific field selection instead of selecting all fields
- Enable caching for frequently executed queries

```typescript
// Performance-optimized query
const optimizedQuery = this.queryBuilder
  .createListRequest<Product>()
  .select("id", "name", "price") // Only needed fields
  .filter((filter) => filter.eq("category", "electronics"))
  .sortAsc("name")
  .paginate(1, 50)
  .options(this.queryBuilder.getPerformanceOptimizedOptions())
  .build();
```

## Advanced Configuration

```typescript
// Custom query options
const customOptions = {
  includeTotals: true,
  timeout: 60000,
  cache: {
    enabled: true,
    ttl: 600,
  },
  trackTotalHits: true,
};

const query = this.queryBuilder.createListRequest<Product>().options(customOptions).build();
```

## Browser Support

- Chrome >= 90
- Firefox >= 88
- Safari >= 14
- Edge >= 90

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
