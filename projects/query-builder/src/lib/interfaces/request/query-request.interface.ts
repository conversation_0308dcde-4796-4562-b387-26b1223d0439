import { DeletedFilter } from '../../enums';
import { IFilterGroup } from '../filter';
import { IPaginationRequest } from './pagination-request.interface';
import { IQueryOptions } from './query-options.interface';
import { IResponseFormat } from './response-format.interface';
import { ISortRequest } from './sort-request.interface';

export interface IQueryRequest {
  pagination?: IPaginationRequest;
  select?: string;
  filters?: IFilterGroup;
  search?: string;
  sort?: ISortRequest[];
  options?: IQueryOptions;
  format?: IResponseFormat;
  deletedFilter?: DeletedFilter;
}
