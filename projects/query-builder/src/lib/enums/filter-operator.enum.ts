/* cspell:ignore notlike notilike notin notcontains notbetween isnotnull isnotempty notregex */
export enum FilterOperator {
  // Equality operators
  Eq = 'eq', // equals
  Ne = 'ne', // not equals

  // Comparison operators
  Gt = 'gt', // greater than
  Gte = 'gte', // greater than or equal
  Lt = 'lt', // less than
  Lte = 'lte', // less than or equal

  // String operators
  Like = 'like', // like (case-sensitive)
  Ilike = 'ilike', // like (case-insensitive)
  NotLike = 'notlike', // not like
  NotIlike = 'notilike', // not like (case-insensitive)

  // Array operators
  In = 'in', // in array
  NotIn = 'notin', // not in array

  // Content operators
  Contains = 'contains', // contains
  NotContains = 'notcontains', // not contains
  StartsWith = 'startswith', // starts with
  EndsWith = 'endswith', // ends with

  // Range operators
  Between = 'between', // between two values
  NotBetween = 'notbetween', // not between two values

  // Null operators
  IsNull = 'isnull', // is null
  IsNotNull = 'isnotnull', // is not null

  // Empty operators
  IsEmpty = 'isempty', // is empty
  IsNotEmpty = 'isnotempty', // is not empty

  // Pattern operators
  Regex = 'regex', // regular expression
  NotRegex = 'notregex', // not regular expression
}
