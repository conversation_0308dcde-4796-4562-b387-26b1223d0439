export interface IHasId<T = string | number> {
  readonly id: T;
}

export interface IAuditFields {
  readonly createdAt: Date;
  readonly createdBy?: string;
  readonly updatedAt?: Date;
  readonly updatedBy?: string;
  readonly deletedAt?: Date;
  readonly deletedBy?: string;
  readonly isDeleted: boolean;
}

export interface IVersioned {
  readonly rowVersion: string | number;
}

export interface IMetadata {
  readonly metadata?: Record<string, unknown>;
}

export interface ISoftDelete {
  readonly isDeleted: boolean;
  readonly deletedAt?: Date;
  readonly deletedBy?: string;
}

export interface IBaseEntity<T = string | number>
  extends IHasId<T>,
    IAuditFields,
    IVersioned,
    IMetadata {}
