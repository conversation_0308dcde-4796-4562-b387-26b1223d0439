import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'range' })
export class RangePipe implements PipeTransform {
  /**
   * Transform input into a range array
   * @param value Number or array [start, end, step?]
   * @returns Array range
   */
  public transform(value: number | number[] | null | undefined): number[] {
    if (value === null || value === undefined) return [];

    let start: number = 0;
    let end: number;
    let step: number = 1;

    if (typeof value === 'number') {
      // Single number: create array from 1 to value
      start = 1;
      end = value;
    } else if (Array.isArray(value)) {
      // Array format: [start, end, step?]
      if (value.length === 2) {
        [start, end] = value;
      } else if (value.length === 3) {
        [start, end, step] = value;
      } else {
        return [];
      }
    } else {
      return [];
    }

    // Validate step
    if (step === 0) {
      return [];
    }

    // Generate range
    const range: number[] = [];

    if (step > 0) {
      for (let i = start; i < end; i += step) {
        range.push(i);
      }
    } else {
      for (let i = start; i > end; i += step) {
        range.push(i);
      }
    }

    return range;
  }
}
