import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'randomNumber' })
export class RandomNumberPipe implements PipeTransform {
  /**
   * Generate a random number between min and max (inclusive)
   * @param min Minimum value
   * @param max Maximum value
   */
  public transform(min: number = 0, max: number = 1): number {
    if (min > max) [min, max] = [max, min]; // Swap if wrong order
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
}
