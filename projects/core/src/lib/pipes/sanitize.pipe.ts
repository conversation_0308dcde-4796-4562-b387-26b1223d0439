import { Pipe, PipeTransform } from '@angular/core';
import {
  <PERSON>Sanitizer,
  SafeHtml,
  SafeStyle,
  SafeScript,
  SafeUrl,
  SafeResourceUrl,
} from '@angular/platform-browser';

export type SafeType = 'html' | 'style' | 'script' | 'url' | 'resourceUrl';

@Pipe({ name: 'sanitize' })
export class SanitizePipe implements PipeTransform {
  public constructor(private readonly sanitizer: DomSanitizer) {}

  public transform(
    value: any,
    type: SafeType = 'html'
  ): SafeHtml | SafeStyle | SafeScript | SafeUrl | SafeResourceUrl {
    if (!value) {
      return '';
    }

    return this.getSafeValue(value, type);
  }

  private getSafeValue(
    value: any,
    type: SafeType
  ): SafeHtml | SafeStyle | SafeScript | SafeUrl | SafeResourceUrl {
    switch (type) {
      case 'html':
        return this.sanitizer.bypassSecurityTrustHtml(value);
      case 'style':
        return this.sanitizer.bypassSecurityTrustStyle(value);
      case 'script':
        return this.sanitizer.bypassSecurityTrustScript(value);
      case 'url':
        return this.sanitizer.bypassSecurityTrustUrl(value);
      case 'resourceUrl':
        return this.sanitizer.bypassSecurityTrustResourceUrl(value);
      default:
        return this.sanitizer.bypassSecurityTrustHtml(value);
    }
  }
}
