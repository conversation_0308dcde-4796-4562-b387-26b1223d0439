import { Pipe, PipeTransform } from '@angular/core';
import { MultiLanguageText } from '../types';
import { LanguageConfigService } from '../features/language-config/services';

@Pipe({ name: 'multiLangText' })
export class MultiLanguageTextPipe implements PipeTransform {
  public constructor(
    private readonly languageConfigService: LanguageConfigService
  ) {}

  public transform(
    text: MultiLanguageText | string | null | undefined,
    language?: string,
    fallbackLanguages?: string[]
  ): string {
    // Handle null/undefined/empty
    if (!text) return '';

    // Handle string input (already localized)
    if (typeof text === 'string') return text;

    // Handle non-object input
    if (typeof text !== 'object') return String(text);

    // Determine target language
    const targetLanguage =
      language || this.languageConfigService.getCurrentLanguage();

    // Try target language first
    if (text[targetLanguage]) return text[targetLanguage];

    // Try fallback languages if provided
    if (fallbackLanguages && fallbackLanguages.length > 0) {
      for (const fallbackLang of fallbackLanguages) {
        if (text[fallbackLang]) return text[fallbackLang];
      }
    }

    if (text['en']) return text['en'];

    // Return first available value as last resort
    const firstValue = Object.values(text)[0];
    return firstValue || '';
  }
}
