import { ITokenProvider } from '../interfaces';

export class CookieTokenProvider implements ITokenProvider {
  constructor(private readonly tokenKey: string = 'authToken') {}

  public getToken(): string | null {
    if (typeof document !== 'undefined') {
      const name = this.tokenKey + '=';
      const decodedCookie = decodeURIComponent(document.cookie);
      const cookies = decodedCookie.split(';');

      for (let cookie of cookies) {
        if (cookie.startsWith(name)) {
          return cookie.substring(name.length, cookie.length);
        }
      }
    }
    return null;
  }

  public setToken(token: string): void {
    if (typeof document !== 'undefined') {
      document.cookie = `${this.tokenKey}=${token}; path=/; secure; samesite=strict`;
    }
  }

  public removeToken(): void {
    if (typeof document !== 'undefined') {
      document.cookie = `${this.tokenKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    }
  }
}
