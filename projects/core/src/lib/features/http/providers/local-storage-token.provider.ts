import { ITokenProvider } from '../interfaces';

export class LocalStorageTokenProvider implements ITokenProvider {
  public constructor(private readonly tokenKey: string = 'authToken') {}

  public getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.tokenKey);
  }

  public setToken(token: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.tokenKey, token);
  }

  public removeToken(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.tokenKey);
  }
}
