import { ITokenProvider } from '../interfaces';

export class SessionStorageTokenProvider implements ITokenProvider {
  constructor(private readonly tokenKey: string = 'authToken') {}

  public getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return sessionStorage.getItem(this.tokenKey);
  }

  public setToken(token: string): void {
    if (typeof window === 'undefined') return;
    sessionStorage.setItem(this.tokenKey, token);
  }

  public removeToken(): void {
    if (typeof window === 'undefined') return;
    sessionStorage.removeItem(this.tokenKey);
  }
}
