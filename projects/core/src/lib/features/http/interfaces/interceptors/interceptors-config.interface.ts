export interface IInterceptorConfig {
  baseUrl?: string;
  defaultTimeout?: number;
  authConfig?: {
    headerName?: string;
    tokenPrefix?: string;
    excludePaths?: string[];
  };
  cacheConfig?: {
    defaultTtl?: number;
    maxSize?: number;
    excludePaths?: string[];
  };
  retryConfig?: {
    maxAttempts?: number;
    delay?: number;
    excludeStatusCodes?: number[];
  };
  errorConfig?: {
    enableGlobalHandler?: boolean;
    logErrors?: boolean;
    showNotifications?: boolean;
  };
  loadingConfig?: {
    enabled?: boolean;
    excludePaths?: string[];
  };
}
