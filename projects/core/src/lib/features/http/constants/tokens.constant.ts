import { InjectionToken } from '@angular/core';
import { IInterceptorConfig, ITokenProvider } from '../interfaces';
import { IErrorNotificationService } from '../interceptors';

export const INTERCEPTOR_CONFIG = new InjectionToken<IInterceptorConfig>(
  'InterceptorConfig'
);

export const TOKEN_PROVIDER = new InjectionToken<ITokenProvider>(
  'TokenProvider'
);

export const ERROR_NOTIFICATION_SERVICE =
  new InjectionToken<IErrorNotificationService>('ErrorNotificationService');
