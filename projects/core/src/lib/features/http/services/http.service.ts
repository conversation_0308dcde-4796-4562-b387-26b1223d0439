import { Injectable } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpParams,
  HttpEvent,
  HttpResponse,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { INTERCEPTOR_HEADERS } from '../constants';

// Create type aliases to reduce union type repetition
type HttpBodyResponse<T> = Observable<T>;
type HttpFullResponse<T> = Observable<HttpResponse<T>>;
type HttpEventResponse<T> = Observable<HttpEvent<T>>;
type HttpAnyResponse<T> =
  | HttpBodyResponse<T>
  | HttpFullResponse<T>
  | HttpEventResponse<T>;

export interface HttpOptions {
  headers?: Record<string, string>;
  params?: Record<string, any>;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
  observe?: 'body' | 'response' | 'events';
  cache?: boolean;
  cacheTtl?: number;
  cacheKey?: string;
  retry?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
  skipAuth?: boolean;
  skipLoading?: boolean;
  skipInterceptors?: string[];
}

// Specific option types for overloads
type BodyOptions = HttpOptions & { observe?: 'body' };
type ResponseOptions = HttpOptions & { observe: 'response' };
type EventsOptions = HttpOptions & { observe: 'events' };

@Injectable()
export class HttpService {
  public constructor(private readonly http: HttpClient) {}

  // GET method overloads
  public get<T>(url: string, options?: BodyOptions): HttpBodyResponse<T>;
  public get<T>(url: string, options: ResponseOptions): HttpFullResponse<T>;
  public get<T>(url: string, options: EventsOptions): HttpEventResponse<T>;
  public get<T>(url: string, options: HttpOptions = {}): HttpAnyResponse<T> {
    return this.executeRequest<T>('GET', url, null, options);
  }

  // POST method overloads
  public post<T>(
    url: string,
    body: any,
    options?: BodyOptions
  ): HttpBodyResponse<T>;
  public post<T>(
    url: string,
    body: any,
    options: ResponseOptions
  ): HttpFullResponse<T>;
  public post<T>(
    url: string,
    body: any,
    options: EventsOptions
  ): HttpEventResponse<T>;
  public post<T>(
    url: string,
    body: any,
    options: HttpOptions = {}
  ): HttpAnyResponse<T> {
    return this.executeRequest<T>('POST', url, body, options);
  }

  // PUT method overloads
  public put<T>(
    url: string,
    body: any,
    options?: BodyOptions
  ): HttpBodyResponse<T>;
  public put<T>(
    url: string,
    body: any,
    options: ResponseOptions
  ): HttpFullResponse<T>;
  public put<T>(
    url: string,
    body: any,
    options: EventsOptions
  ): HttpEventResponse<T>;
  public put<T>(
    url: string,
    body: any,
    options: HttpOptions = {}
  ): HttpAnyResponse<T> {
    return this.executeRequest<T>('PUT', url, body, options);
  }

  // PATCH method overloads
  public patch<T>(
    url: string,
    body: any,
    options?: BodyOptions
  ): HttpBodyResponse<T>;
  public patch<T>(
    url: string,
    body: any,
    options: ResponseOptions
  ): HttpFullResponse<T>;
  public patch<T>(
    url: string,
    body: any,
    options: EventsOptions
  ): HttpEventResponse<T>;
  public patch<T>(
    url: string,
    body: any,
    options: HttpOptions = {}
  ): HttpAnyResponse<T> {
    return this.executeRequest<T>('PATCH', url, body, options);
  }

  // DELETE method overloads
  public delete<T>(
    url: string,
    body: any,
    options?: BodyOptions
  ): HttpBodyResponse<T>;
  public delete<T>(
    url: string,
    body: any,
    options: ResponseOptions
  ): HttpFullResponse<T>;
  public delete<T>(
    url: string,
    body: any,
    options: EventsOptions
  ): HttpEventResponse<T>;
  public delete<T>(
    url: string,
    body: any,
    options: HttpOptions = {}
  ): HttpAnyResponse<T> {
    return this.executeRequest<T>('DELETE', url, body, options);
  }

  // Upload a single file
  public upload<T>(
    url: string,
    file: File,
    options?: BodyOptions
  ): HttpBodyResponse<T>;
  public upload<T>(
    url: string,
    file: File,
    options: ResponseOptions
  ): HttpFullResponse<T>;
  public upload<T>(
    url: string,
    file: File,
    options: EventsOptions
  ): HttpEventResponse<T>;
  public upload<T>(
    url: string,
    file: File,
    options: HttpOptions = {}
  ): HttpAnyResponse<T> {
    const formData = this.createSingleFileFormData(file);
    const uploadOptions = this.prepareUploadOptions(options);
    return this.executeRequest<T>('POST', url, formData, uploadOptions);
  }

  // Upload multiple files
  public uploadMultiple<T>(
    url: string,
    files: File[],
    fieldName: string,
    options?: BodyOptions
  ): HttpBodyResponse<T>;
  public uploadMultiple<T>(
    url: string,
    files: File[],
    fieldName: string,
    options: ResponseOptions
  ): HttpFullResponse<T>;
  public uploadMultiple<T>(
    url: string,
    files: File[],
    fieldName: string,
    options: EventsOptions
  ): HttpEventResponse<T>;
  public uploadMultiple<T>(
    url: string,
    files: File[],
    fieldName: string = 'files',
    options: HttpOptions = {}
  ): HttpAnyResponse<T> {
    const formData = this.createMultipleFileFormData(files, fieldName);
    const uploadOptions = this.prepareMultipleUploadOptions(options);
    return this.executeRequest<T>('POST', url, formData, uploadOptions);
  }

  // Download a file
  public download(
    url: string,
    filename?: string,
    options: HttpOptions = {}
  ): Observable<Blob> {
    const downloadOptions = this.prepareDownloadOptions({
      ...options,
      responseType: 'blob',
    });

    return this.executeRequest<Blob>('GET', url, null, downloadOptions).pipe(
      tap((blob: Blob) => {
        if (filename) {
          this.saveFile(blob, filename);
        }
      })
    );
  }

  // Private executeRequest method - single implementation that handles all cases
  private executeRequest<T>(
    method: string,
    url: string,
    body: any,
    options: HttpOptions
  ): Observable<any> {
    const httpOptions = this.buildHttpOptions(options);

    switch (method.toUpperCase()) {
      case 'GET':
        return this.http.get<T>(url, httpOptions);
      case 'POST':
        return this.http.post<T>(url, body, httpOptions);
      case 'PUT':
        return this.http.put<T>(url, body, httpOptions);
      case 'PATCH':
        return this.http.patch<T>(url, body, httpOptions);
      case 'DELETE':
        return this.http.delete<T>(url, httpOptions);
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }

  private buildHttpOptions(options: HttpOptions): any {
    const headers = new HttpHeaders(this.buildHeaders(options));
    const params = options.params
      ? new HttpParams({ fromObject: this.stringifyParams(options.params) })
      : undefined;

    return {
      headers,
      params,
      observe: options.observe || 'body',
      responseType: options.responseType || 'json',
    };
  }

  private buildHeaders(options: HttpOptions): Record<string, string> {
    const headers: Record<string, string> = { ...options.headers };

    if (options.cache !== undefined)
      headers[INTERCEPTOR_HEADERS.CACHE_CONTROL] = options.cache
        ? 'cache'
        : 'no-cache';
    if (options.cacheTtl)
      headers[INTERCEPTOR_HEADERS.CACHE_TTL] = options.cacheTtl.toString();
    if (options.cacheKey)
      headers[INTERCEPTOR_HEADERS.CACHE_KEY] = options.cacheKey;
    if (options.retry !== undefined) {
      headers[INTERCEPTOR_HEADERS.RETRY_CONTROL] = options.retry
        ? 'retry'
        : 'no-retry';
    }
    if (options.retryAttempts)
      headers[INTERCEPTOR_HEADERS.RETRY_ATTEMPTS] =
        options.retryAttempts.toString();
    if (options.retryDelay)
      headers[INTERCEPTOR_HEADERS.RETRY_DELAY] = options.retryDelay.toString();
    if (options.timeout)
      headers[INTERCEPTOR_HEADERS.TIMEOUT] = options.timeout.toString();
    if (options.skipAuth) headers[INTERCEPTOR_HEADERS.SKIP_AUTH] = 'true';
    if (options.skipLoading) headers[INTERCEPTOR_HEADERS.SKIP_LOADING] = 'true';
    if (options.skipInterceptors?.length) {
      headers[INTERCEPTOR_HEADERS.SKIP_INTERCEPTOR] =
        options.skipInterceptors.join(',');
    }

    return headers;
  }

  private stringifyParams(params: Record<string, any>): Record<string, string> {
    const result: Record<string, string> = {};
    Object.keys(params).forEach((key) => {
      const val = params[key];
      if (val !== null && val !== undefined) result[key] = String(val);
    });
    return result;
  }

  private createSingleFileFormData(file: File): FormData {
    const formData = new FormData();
    formData.append('file', file);
    return formData;
  }

  private createMultipleFileFormData(
    files: File[],
    fieldName: string
  ): FormData {
    const formData = new FormData();
    files.forEach((file, index) =>
      formData.append(`${fieldName}[${index}]`, file)
    );
    return formData;
  }

  private prepareUploadOptions(options: HttpOptions): HttpOptions {
    const uploadOptions = {
      ...options,
      skipAuth: false,
      headers: { ...options.headers },
    };

    delete uploadOptions.headers?.['Content-Type'];
    return uploadOptions;
  }

  private prepareMultipleUploadOptions(options: HttpOptions): HttpOptions {
    const uploadOptions = {
      ...options,
      timeout: 120000,
      headers: { ...options.headers },
    };

    delete uploadOptions.headers?.['Content-Type'];
    return uploadOptions;
  }

  private prepareDownloadOptions(options: HttpOptions): HttpOptions {
    return {
      ...options,
      headers: { ...options.headers, Accept: 'application/octet-stream' },
    };
  }

  private saveFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
