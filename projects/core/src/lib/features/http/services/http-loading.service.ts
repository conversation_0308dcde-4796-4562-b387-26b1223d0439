import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { LoadingState } from '../interfaces/http';

@Injectable()
export class HttpLoadingService {
  private readonly loadingSubject = new BehaviorSubject<LoadingState>({
    isLoading: false,
    activeRequests: 0,
    requestIds: new Set(),
    requests: new Map(),
  });

  public loading$ = this.loadingSubject.asObservable();

  public startRequest(requestId: string, url: string, method: string): void {
    const currentState = this.loadingSubject.value;
    const newRequestIds = new Set(currentState.requestIds);
    const newRequests = new Map(currentState.requests);

    newRequestIds.add(requestId);
    newRequests.set(requestId, { url, method, startTime: Date.now() });

    const newState: LoadingState = {
      isLoading: true,
      activeRequests: currentState.activeRequests + 1,
      requestIds: newRequestIds,
      requests: newRequests,
    };

    this.loadingSubject.next(newState);
  }

  public endRequest(requestId: string): void {
    const currentState = this.loadingSubject.value;
    const newRequestIds = new Set(currentState.requestIds);
    const newRequests = new Map(currentState.requests);

    newRequestIds.delete(requestId);
    const request = newRequests.get(requestId);
    if (request) {
      const duration = Date.now() - request.startTime;
      console.log(
        `⏱️ Request completed: ${request.method} ${request.url} (${duration}ms)`
      );
      newRequests.delete(requestId);
    }

    const activeRequests = Math.max(0, currentState.activeRequests - 1);
    const newState: LoadingState = {
      isLoading: activeRequests > 0,
      activeRequests,
      requestIds: newRequestIds,
      requests: newRequests,
    };

    this.loadingSubject.next(newState);
  }

  public getCurrentState(): LoadingState {
    return this.loadingSubject.value;
  }

  public isRequestActive(requestId: string): boolean {
    return this.loadingSubject.value.requestIds.has(requestId);
  }
}
