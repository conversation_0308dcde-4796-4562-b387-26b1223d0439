import { Inject, Injectable, Optional } from '@angular/core';
import {
  HttpEvent,
  HttpInterceptor,
  HttpHandler,
  HttpRequest,
} from '@angular/common/http';
import { from, Observable } from 'rxjs';
import {
  INTERCEPTOR_CONFIG,
  TOKEN_PROVIDER,
} from '../constants/tokens.constant';
import { IInterceptorConfig, ITokenProvider } from '../interfaces';
import { LocalStorageTokenProvider } from '../providers';
import { switchMap } from 'rxjs/operators';
import { shouldSkip } from '../utils';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private readonly authConfig: IInterceptorConfig['authConfig'];

  private readonly defaultAuthConfig: IInterceptorConfig['authConfig'] = {
    headerName: 'Authorization',
    tokenPrefix: 'Bearer',
    excludePaths: ['/auth/login', '/auth/register', '/public'],
  };

  private readonly defaultTokenProvider = new LocalStorageTokenProvider(
    'authToken'
  );

  public constructor(
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    private readonly config?: IInterceptorConfig,
    @Optional()
    @Inject(TOKEN_PROVIDER)
    private readonly tokenProvider?: ITokenProvider
  ) {
    this.authConfig = this.initializeAuthConfig();
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Skip if interceptor should be bypassed
    if (shouldSkip(req, 'Auth')) {
      return next.handle(req);
    }

    // Skip if no token provider available
    if (!this.getTokenProvider()) {
      return next.handle(req);
    }

    // Skip auth for excluded paths
    if (this.isExcludedPath(req.url)) {
      return next.handle(req);
    }

    // Handle both sync and async token providers
    return from(this.getTokenAsync()).pipe(
      switchMap((token) => {
        if (!token) return next.handle(req);

        const authReq = req.clone({
          headers: req.headers.set(
            this.getHeaderName(),
            `${this.getTokenPrefix()} ${token}`
          ),
        });

        return next.handle(authReq);
      })
    );
  }

  public async setToken(token: string): Promise<void> {
    const provider = this.getTokenProvider();
    if (provider?.setToken) provider.setToken(token);
  }

  public async removeToken(): Promise<void> {
    const provider = this.getTokenProvider();
    if (provider?.removeToken) provider.removeToken();
  }

  private initializeAuthConfig(): IInterceptorConfig['authConfig'] {
    if (this.config?.authConfig) {
      return {
        ...this.defaultAuthConfig,
        ...this.config.authConfig,
      };
    }

    // If no config but token provider is injected, use default config
    if (this.tokenProvider) return this.defaultAuthConfig;

    return undefined;
  }

  private async getTokenAsync(): Promise<string | null> {
    const provider = this.getTokenProvider();
    if (!provider) return null;

    const token = provider.getToken();
    return token instanceof Promise ? await token : token;
  }

  private getTokenProvider(): ITokenProvider | undefined {
    // Priority: injected provider > default provider (only if config exists)
    return (
      this.tokenProvider ||
      (this.authConfig ? this.defaultTokenProvider : undefined)
    );
  }

  private getHeaderName(): string {
    return this.authConfig?.headerName || this.defaultAuthConfig!.headerName!;
  }

  private getTokenPrefix(): string {
    return this.authConfig?.tokenPrefix || this.defaultAuthConfig!.tokenPrefix!;
  }

  private isExcludedPath(url: string): boolean {
    const excludePaths =
      this.authConfig?.excludePaths || this.defaultAuthConfig!.excludePaths;
    return excludePaths?.some((path) => url.includes(path)) || false;
  }
}
