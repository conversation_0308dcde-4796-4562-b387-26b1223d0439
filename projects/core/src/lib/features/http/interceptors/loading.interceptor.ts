import {
  HttpInterceptor,
  HttpRequest,
  Http<PERSON><PERSON><PERSON>,
  HttpEvent,
} from '@angular/common/http';
import { Injectable, Optional, Inject } from '@angular/core';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { INTERCEPTOR_HEADERS, INTERCEPTOR_CONFIG } from '../constants';
import { IInterceptorConfig } from '../interfaces';
import { shouldSkip, removeCustomHeaders } from '../utils';
import { HttpLoadingService } from '../services';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  private readonly loadingConfig: IInterceptorConfig['loadingConfig'];

  private readonly defaultLoadingConfig: IInterceptorConfig['loadingConfig'] = {
    enabled: true,
    excludePaths: [],
  };

  public constructor(
    private readonly httpLoadingService: HttpLoadingService,
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    readonly config?: IInterceptorConfig
  ) {
    this.loadingConfig = config?.loadingConfig
      ? { ...this.defaultLoadingConfig, ...config.loadingConfig }
      : undefined;
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Skip if interceptor should be bypassed
    if (shouldSkip(req, 'Loading')) return next.handle(req);

    // Check if loading is explicitly enabled via headers
    const skipLoading = req.headers.get(INTERCEPTOR_HEADERS.SKIP_LOADING);
    const hasLoadingHeaders = req.headers.has(INTERCEPTOR_HEADERS.REQUEST_ID);

    // Skip if no config and no loading headers and not explicitly enabled (default behavior)
    if (!this.loadingConfig && !hasLoadingHeaders && skipLoading !== 'false')
      return next.handle(this.removeCustomHeaders(req));

    // Skip if loading tracking is explicitly disabled
    if (skipLoading === 'true')
      return next.handle(this.removeCustomHeaders(req));

    // Skip loading for excluded paths
    if (this.isExcludedPath(req.url))
      return next.handle(this.removeCustomHeaders(req));

    // Get or generate request ID
    const requestId =
      req.headers.get(INTERCEPTOR_HEADERS.REQUEST_ID) ??
      this.generateRequestId();

    // Start loading
    this.httpLoadingService.startRequest(requestId, req.url, req.method);

    return next
      .handle(this.removeCustomHeaders(req))
      .pipe(finalize(() => this.httpLoadingService.endRequest(requestId)));
  }

  private generateRequestId(): string {
    return (
      'req_' + Math.random().toString(36).substring(2) + Date.now().toString(36)
    );
  }

  private isExcludedPath(url: string): boolean {
    const excludePaths =
      this.loadingConfig?.excludePaths ||
      this.defaultLoadingConfig!.excludePaths;
    return excludePaths?.some((path) => url.includes(path)) || false;
  }

  private removeCustomHeaders(req: HttpRequest<any>): HttpRequest<any> {
    return removeCustomHeaders(req, [
      INTERCEPTOR_HEADERS.SKIP_LOADING,
      INTERCEPTOR_HEADERS.REQUEST_ID,
    ]);
  }
}
