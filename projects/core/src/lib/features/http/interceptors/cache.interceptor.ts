import {
  HttpRequest,
  HttpH<PERSON>ler,
  HttpEvent,
  HttpResponse,
  HttpInterceptor,
} from '@angular/common/http';
import { Injectable, Optional, Inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { INTERCEPTOR_HEADERS } from '../constants';
import { INTERCEPTOR_CONFIG } from '../constants/tokens.constant';
import { IInterceptorConfig } from '../interfaces';
import { shouldSkip } from '../utils';
import { removeCustomHeaders } from '../utils/remove-custom-headers.util';

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
  url: string;
  headers?: any;
}

@Injectable()
export class CacheInterceptor implements HttpInterceptor {
  private readonly cache = new Map<string, CacheEntry>();
  private readonly cacheConfig: IInterceptorConfig['cacheConfig'];

  private readonly defaultCacheConfig: IInterceptorConfig['cacheConfig'] = {
    defaultTtl: 300000, // 5 minutes
    maxSize: 100,
    excludePaths: ['/auth', '/upload', '/download'],
  };

  constructor(
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    readonly config?: IInterceptorConfig
  ) {
    this.cacheConfig = config?.cacheConfig
      ? { ...this.defaultCacheConfig, ...config.cacheConfig }
      : undefined;
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Only cache GET requests
    if (req.method !== 'GET') return next.handle(req);

    // Skip if interceptor should be bypassed
    if (shouldSkip(req, 'Cache')) return next.handle(req);

    // Check if cache is explicitly enabled via headers
    const cacheControl = req.headers.get(INTERCEPTOR_HEADERS.CACHE_CONTROL);
    const hasCacheHeaders =
      req.headers.has(INTERCEPTOR_HEADERS.CACHE_TTL) ||
      req.headers.has(INTERCEPTOR_HEADERS.CACHE_KEY);

    // Skip if no config and no cache headers (default behavior)
    if (!this.cacheConfig && !hasCacheHeaders && cacheControl !== 'cache') {
      return next.handle(req);
    }

    // Skip caching for excluded paths
    if (this.isExcludedPath(req.url)) {
      return next.handle(req);
    }

    // Check if cache is explicitly disabled
    if (cacheControl === 'no-cache') {
      return next.handle(this.removeCustomHeaders(req));
    }

    // Generate cache key
    const cacheKey = this.generateCacheKey(req);

    // Check cache
    const cached = this.getFromCache(cacheKey);
    if (cached && cacheControl !== 'no-store') {
      console.log(`🎯 Cache HIT: ${cacheKey}`);
      return of(
        new HttpResponse({
          body: cached.data,
          status: 200,
          statusText: 'OK (from cache)',
          url: req.url,
          headers: cached.headers,
        })
      );
    }

    console.log(`❌ Cache MISS: ${cacheKey}`);

    // Get TTL from header or use default
    const ttlHeader = req.headers.get(INTERCEPTOR_HEADERS.CACHE_TTL);
    const ttl = ttlHeader ? parseInt(ttlHeader, 10) : this.getTtl();

    // Execute request and cache response
    return next.handle(this.removeCustomHeaders(req)).pipe(
      tap((event) => {
        if (event instanceof HttpResponse && event.status === 200) {
          this.setCache(cacheKey, event.body, ttl, req.url, event.headers);
          console.log(`💾 Cached: ${cacheKey}`);
        }
      })
    );
  }

  public clearCache(pattern?: string): void {
    if (pattern) {
      const keysToDelete = Array.from(this.cache.keys()).filter(
        (key) =>
          key.includes(pattern) || this.cache.get(key)?.url.includes(pattern)
      );
      keysToDelete.forEach((key) => this.cache.delete(key));
      console.log(
        `🗑️ Cleared ${keysToDelete.length} cache entries matching: ${pattern}`
      );
    } else {
      this.cache.clear();
      console.log('🗑️ Cleared all cache');
    }
  }

  public getCacheStats(): { size: number; maxSize: number; entries: string[] } {
    return {
      size: this.cache.size,
      maxSize: this.getMaxSize(),
      entries: Array.from(this.cache.keys()),
    };
  }

  private getTtl(): number {
    return this.cacheConfig?.defaultTtl || this.defaultCacheConfig!.defaultTtl!;
  }

  private getMaxSize(): number {
    return this.cacheConfig?.maxSize || this.defaultCacheConfig!.maxSize!;
  }

  private generateCacheKey(req: HttpRequest<any>): string {
    // Use custom cache key if provided
    const customKey = req.headers.get(INTERCEPTOR_HEADERS.CACHE_KEY);
    if (customKey) return customKey;

    // Generate key from URL, params, and relevant headers
    let key = `${req.method}:${req.urlWithParams}`;

    // Include auth header in cache key (different users = different cache)
    const authHeader = req.headers.get('Authorization');
    if (authHeader) {
      const tokenPart = authHeader.split(' ')[1]?.substring(0, 10); // First 10 chars
      key += `:auth=${tokenPart}`;
    }

    return key;
  }

  private getFromCache(key: string): CacheEntry | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      console.log(`⏰ Cache expired: ${key}`);
      return null;
    }

    return entry;
  }

  private setCache(
    key: string,
    data: any,
    ttl: number,
    url: string,
    headers?: any
  ): void {
    // Implement cache size limit (LRU)
    if (this.cache.size >= this.getMaxSize()) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      url,
      headers,
    });
  }

  private isExcludedPath(url: string): boolean {
    const excludePaths =
      this.cacheConfig?.excludePaths || this.defaultCacheConfig!.excludePaths;
    return excludePaths?.some((path) => url.includes(path)) || false;
  }

  private removeCustomHeaders(req: HttpRequest<any>): HttpRequest<any> {
    return removeCustomHeaders(req, [
      INTERCEPTOR_HEADERS.CACHE_CONTROL,
      INTERCEPTOR_HEADERS.CACHE_TTL,
      INTERCEPTOR_HEADERS.CACHE_KEY,
    ]);
  }
}
