import { Injectable, Optional, Inject } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
  HttpResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  ERROR_NOTIFICATION_SERVICE,
  INTERCEPTOR_CONFIG,
} from '../constants/tokens.constant';
import { IInterceptorConfig } from '../interfaces';
import { shouldSkip } from '../utils';
import { IApiResponse } from '../../../interfaces';

export interface IErrorNotificationService {
  showError(message: string, errors?: string[]): void;
  showWarning(message: string): void;
  showSuccess(message: string): void;
}

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  private readonly errorConfig: IInterceptorConfig['errorConfig'];

  private readonly defaultErrorConfig: IInterceptorConfig['errorConfig'] = {
    enableGlobalHandler: false,
    logErrors: true,
    showNotifications: false,
  };

  public constructor(
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    readonly config?: IInterceptorConfig,
    @Optional()
    @Inject(ERROR_NOTIFICATION_SERVICE)
    private readonly notificationService?: IErrorNotificationService
  ) {
    this.errorConfig = config?.errorConfig
      ? { ...this.defaultErrorConfig, ...config.errorConfig }
      : undefined;
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Skip if interceptor should be bypassed
    if (shouldSkip(req, 'Error')) {
      return next.handle(req);
    }

    // Skip if no config and global handler not explicitly enabled
    if (!this.errorConfig && !this.isErrorHandlingEnabled()) {
      return next.handle(req);
    }

    // Add request ID for tracking
    const requestId = this.generateRequestId();
    const modifiedReq = req.clone({
      headers: req.headers.set('X-Request-ID', requestId),
    });

    return next.handle(modifiedReq).pipe(
      tap((event) => {
        // Handle successful responses that might contain error information
        if (event instanceof HttpResponse && event.body) {
          this.handleApiResponse(event.body, req, requestId);
        }
      }),
      catchError((error) => {
        this.handleHttpError(error, modifiedReq, requestId);
        return throwError(() => error);
      })
    );
  }

  private handleApiResponse(
    responseBody: any,
    req: HttpRequest<any>,
    requestId: string
  ): void {
    // Check if response follows your ApiResponse structure
    if (this.isApiResponse(responseBody) && !responseBody.success) {
      this.logError({
        type: 'API_ERROR',
        requestId,
        method: req.method,
        url: req.url,
        message: responseBody.message || 'API operation failed',
        errors: responseBody.errors,
        timestamp: new Date().toISOString(),
      });

      if (this.shouldShowNotifications() && this.notificationService) {
        this.notificationService.showError(
          responseBody.message || 'Operation failed',
          responseBody.errors
        );
      }
    }
  }

  private handleHttpError(
    error: HttpErrorResponse,
    req: HttpRequest<any>,
    requestId: string
  ): void {
    const errorInfo = this.extractErrorInfo(error, req, requestId);

    if (this.shouldLogErrors()) {
      this.logError(errorInfo);
    }

    if (this.shouldShowNotifications() && this.notificationService) {
      this.showErrorNotification(error, errorInfo);
    }

    // Handle specific error cases
    this.handleSpecificErrors(error);
  }

  private extractErrorInfo(
    error: HttpErrorResponse,
    req: HttpRequest<any>,
    requestId: string
  ) {
    let message = 'An unexpected error occurred';
    let errors: string[] = [];

    // Try to parse backend ApiResponse format
    if (error.error && this.isApiResponse(error.error)) {
      message = error.error.message || message;
      errors = error.error.errors || [];
    } else if (error.error instanceof ErrorEvent) {
      // Client-side error
      message = error.error.message;
    } else if (typeof error.error === 'string') {
      message = error.error;
    } else if (error.message) {
      message = error.message;
    }

    return {
      type: 'HTTP_ERROR' as const,
      requestId,
      method: req.method,
      url: req.url,
      status: error.status,
      statusText: error.statusText,
      message,
      errors,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      requestBody: req.body,
    };
  }

  private showErrorNotification(
    error: HttpErrorResponse,
    errorInfo: any
  ): void {
    if (!this.notificationService) return;

    switch (error.status) {
      case 400:
        this.notificationService.showError(
          errorInfo.message || 'Invalid request',
          errorInfo.errors
        );
        break;

      case 401:
        this.notificationService.showWarning('Please log in again');
        break;

      case 403:
        this.notificationService.showWarning('Access denied');
        break;

      case 404:
        this.notificationService.showError('Resource not found');
        break;

      case 422:
        this.notificationService.showError(
          'Validation failed',
          errorInfo.errors
        );
        break;

      case 500:
        this.notificationService.showError(
          'Server error occurred. Please try again later.'
        );
        break;

      case 0:
        this.notificationService.showError(
          'Connection failed. Please check your internet connection.'
        );
        break;

      default:
        if (error.status >= 400) {
          this.notificationService.showError(
            errorInfo.message,
            errorInfo.errors
          );
        }
    }
  }

  private handleSpecificErrors(error: HttpErrorResponse): void {
    switch (error.status) {
      case 401:
        this.handleUnauthorized();
        break;

      case 403:
        this.handleForbidden();
        break;

      case 429:
        this.handleTooManyRequests(error);
        break;
    }
  }

  private handleUnauthorized(): void {
    // Clear auth token and potentially redirect to login
    if (typeof window !== 'undefined') {
      console.log('🔒 User unauthorized - tokens cleared');

      // You might want to emit an event or redirect here
      // Example: this.router.navigate(['/login']);
    }
  }

  private handleForbidden(): void {
    console.log('🚫 Access forbidden');
    // Could redirect to access denied page or emit event
  }

  private handleTooManyRequests(error: HttpErrorResponse): void {
    const retryAfter = error.headers.get('Retry-After');
    console.log(`⏳ Rate limited. Retry after: ${retryAfter || 'unknown'}`);
  }

  private logError(errorInfo: any): void {
    console.group(`🚨 ${errorInfo.type} - ${errorInfo.status || 'N/A'}`);
    console.error('Error Details:', errorInfo);
    console.groupEnd();

    // Send to monitoring service in production
    if (this.shouldSendToMonitoring()) {
      this.sendToMonitoring(errorInfo);
    }
  }

  private sendToMonitoring(errorInfo: any): void {
    // Implement your monitoring service integration here
    // Example: Sentry, LogRocket, Application Insights, etc.
    console.log('📊 Sending error to monitoring service:', errorInfo);

    // Example implementation:
    // this.monitoringService.trackError(errorInfo);
  }

  private isApiResponse(obj: any): obj is IApiResponse<any> {
    return (
      obj &&
      typeof obj === 'object' &&
      typeof obj.success === 'boolean' &&
      obj.hasOwnProperty('data')
    );
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private isErrorHandlingEnabled(): boolean {
    // Check if error handling is explicitly enabled via some global flag
    return false; // Default to false for opt-in behavior
  }

  private shouldLogErrors(): boolean {
    return this.errorConfig?.logErrors ?? this.defaultErrorConfig!.logErrors!;
  }

  private shouldShowNotifications(): boolean {
    return (
      this.errorConfig?.showNotifications ??
      this.defaultErrorConfig!.showNotifications!
    );
  }

  private shouldSendToMonitoring(): boolean {
    return (
      typeof window !== 'undefined' && window.location.hostname !== 'localhost'
    );
  }
}
