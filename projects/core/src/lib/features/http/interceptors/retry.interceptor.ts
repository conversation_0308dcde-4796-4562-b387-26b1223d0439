import {
  HttpError<PERSON><PERSON>po<PERSON>,
  Http<PERSON><PERSON>,
  Http<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable, Optional, Inject } from '@angular/core';
import { Observable, throwError, timer } from 'rxjs';
import { retryWhen, concatMap } from 'rxjs/operators';
import { INTERCEPTOR_HEADERS } from '../constants';
import { INTERCEPTOR_CONFIG } from '../constants/tokens.constant';
import { IInterceptorConfig } from '../interfaces';
import { shouldSkip } from '../utils';
import { removeCustomHeaders } from '../utils/remove-custom-headers.util';

@Injectable()
export class RetryInterceptor implements HttpInterceptor {
  private readonly retryConfig: IInterceptorConfig['retryConfig'];

  private readonly defaultRetryConfig: IInterceptorConfig['retryConfig'] = {
    maxAttempts: 3,
    delay: 1000,
    excludeStatusCodes: [400, 401, 403, 404, 422],
  };

  public constructor(
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    readonly config?: IInterceptorConfig
  ) {
    this.retryConfig = config?.retryConfig
      ? { ...this.defaultRetryConfig, ...config.retryConfig }
      : undefined;
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // Skip if interceptor should be bypassed
    if (shouldSkip(req, 'Retry')) return next.handle(req);

    // Check if retry is explicitly enabled via headers
    const retryControl = req.headers.get(INTERCEPTOR_HEADERS.RETRY_CONTROL);
    const hasRetryHeaders =
      req.headers.has(INTERCEPTOR_HEADERS.RETRY_ATTEMPTS) ||
      req.headers.has(INTERCEPTOR_HEADERS.RETRY_DELAY);

    // Skip if no config and no retry headers (default behavior)
    if (!this.retryConfig && !hasRetryHeaders && retryControl !== 'force') {
      return next.handle(req);
    }

    // Skip retry for non-idempotent methods by default
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      if (retryControl !== 'force') {
        return next.handle(this.removeCustomHeaders(req));
      }
    }

    // Check if retry is explicitly disabled
    if (retryControl === 'no-retry') {
      return next.handle(this.removeCustomHeaders(req));
    }

    // Get retry configuration
    const maxAttempts = this.getRetryAttempts(req);
    const delay = this.getRetryDelay(req);

    return next.handle(this.removeCustomHeaders(req)).pipe(
      retryWhen((errors) =>
        errors.pipe(
          concatMap((error, index) => {
            const attemptNumber = index + 1;

            // Check if we should retry
            if (attemptNumber <= maxAttempts && this.shouldRetry(error)) {
              console.log(
                `🔄 Retry ${attemptNumber}/${maxAttempts} for ${req.method} ${req.url}`
              );

              // Calculate delay with exponential backoff
              const retryDelay = delay * Math.pow(2, index);
              return timer(retryDelay);
            }

            // No more retries
            console.log(
              `❌ No retry for ${req.method} ${req.url}:`,
              error.message
            );
            return throwError(() => error);
          })
        )
      )
    );
  }

  private shouldRetry(error: any): boolean {
    if (!(error instanceof HttpErrorResponse)) {
      return false;
    }

    // Don't retry client errors (4xx) except specific cases
    if (this.retryConfig?.excludeStatusCodes?.includes(error.status)) {
      return false;
    }

    // Retry conditions
    return (
      error.status === 0 || // Network error
      error.status >= 500 || // Server error
      error.status === 408 || // Request timeout
      error.status === 429 // Too many requests
    );
  }

  private getRetryAttempts(req: HttpRequest<any>): number {
    const attemptsHeader = req.headers.get(INTERCEPTOR_HEADERS.RETRY_ATTEMPTS);
    return attemptsHeader
      ? parseInt(attemptsHeader, 10)
      : this.retryConfig?.maxAttempts || this.defaultRetryConfig!.maxAttempts!;
  }

  private getRetryDelay(req: HttpRequest<any>): number {
    const delayHeader = req.headers.get(INTERCEPTOR_HEADERS.RETRY_DELAY);
    return delayHeader
      ? parseInt(delayHeader, 10)
      : this.retryConfig?.delay || this.defaultRetryConfig!.delay!;
  }

  private removeCustomHeaders(req: HttpRequest<any>): HttpRequest<any> {
    return removeCustomHeaders(req, [
      INTERCEPTOR_HEADERS.RETRY_CONTROL,
      INTERCEPTOR_HEADERS.RETRY_ATTEMPTS,
      INTERCEPTOR_HEADERS.RETRY_DELAY,
    ]);
  }
}
