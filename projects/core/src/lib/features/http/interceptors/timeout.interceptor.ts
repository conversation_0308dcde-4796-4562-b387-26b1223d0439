import {
  HttpInterceptor,
  HttpRequest,
  HttpHand<PERSON>,
  HttpEvent,
} from '@angular/common/http';
import { Injectable, Optional, Inject } from '@angular/core';
import { Observable } from 'rxjs';
import { timeout } from 'rxjs/operators';
import { INTERCEPTOR_HEADERS } from '../constants';
import { INTERCEPTOR_CONFIG } from '../constants/tokens.constant';
import { IInterceptorConfig } from '../interfaces';
import { shouldSkip } from '../utils';
import { removeCustomHeaders } from '../utils/remove-custom-headers.util';

@Injectable()
export class TimeoutInterceptor implements HttpInterceptor {
  private defaultTimeout?: number;

  public constructor(
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    readonly config?: IInterceptorConfig
  ) {
    if (config?.defaultTimeout) this.defaultTimeout = config.defaultTimeout;
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHand<PERSON>
  ): Observable<HttpEvent<any>> {
    // Get timeout from headers or use default
    const timeoutHeader = req.headers.get(INTERCEPTOR_HEADERS.TIMEOUT);
    const requestTimeout = timeoutHeader
      ? parseInt(timeoutHeader, 10)
      : this.defaultTimeout;

    if (!requestTimeout || shouldSkip(req, 'Timeout')) {
      return next.handle(req);
    }

    // Apply timeout
    return next
      .handle(removeCustomHeaders(req, [INTERCEPTOR_HEADERS.TIMEOUT]))
      .pipe(timeout(requestTimeout));
  }

  public setDefaultTimeout(timeoutMs: number): void {
    this.defaultTimeout = timeoutMs;
  }
}
