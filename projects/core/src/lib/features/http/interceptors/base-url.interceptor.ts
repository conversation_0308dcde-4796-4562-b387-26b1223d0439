import { Injectable, Inject, Optional } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { shouldSkip } from '../utils';
import { IInterceptorConfig } from '../interfaces';
import { INTERCEPTOR_CONFIG } from '../constants/tokens.constant';

@Injectable()
export class BaseUrlInterceptor implements HttpInterceptor {
  private baseUrl?: string;

  public constructor(
    @Optional()
    @Inject(INTERCEPTOR_CONFIG)
    readonly config?: IInterceptorConfig
  ) {
    if (config?.baseUrl) this.setBaseUrl(config.baseUrl);
  }

  public intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    if (
      !this.baseUrl ||
      shouldSkip(req, 'BaseUrl') ||
      this.isAbsoluteUrl(req.url)
    )
      return next.handle(req);

    const modifiedReq = req.clone({ url: this.buildFullUrl(req.url) });

    return next.handle(modifiedReq);
  }

  public setBaseUrl(url: string): void {
    this.baseUrl = url.endsWith('/') ? url.slice(0, -1) : url;
  }

  public getBaseUrl(): string | undefined {
    return this.baseUrl;
  }

  private buildFullUrl(url: string): string {
    const cleanUrl = url.startsWith('/') ? url : '/' + url;
    return this.baseUrl + cleanUrl;
  }

  private isAbsoluteUrl(url: string): boolean {
    return /^https?:\/\//.test(url);
  }
}
