import { ModuleWithProviders, NgModule } from '@angular/core';
import { HttpLoadingService, HttpService } from './services';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import {
  INTERCEPTOR_CONFIG,
  TOKEN_PROVIDER,
  ERROR_NOTIFICATION_SERVICE,
} from './constants/tokens.constant';
import {
  BaseUrlInterceptor,
  AuthInterceptor,
  TimeoutInterceptor,
  LoadingInterceptor,
  CacheInterceptor,
  RetryInterceptor,
  ErrorInterceptor,
} from './interceptors';
import { IInterceptorConfig } from './interfaces';

export interface IHttpModuleConfig {
  config?: IInterceptorConfig;
  tokenProvider?: any; // Class or factory
  notificationService?: any; // Class or factory
}

@NgModule({})
export class HttpModule {
  public static forRoot(
    options: IHttpModuleConfig = {}
  ): ModuleWithProviders<HttpModule> {
    const { config, tokenProvider, notificationService } = options;

    const providers: ModuleWithProviders<HttpModule>['providers'] = [
      HttpService,
      HttpLoadingService,
    ];

    // Configuration
    if (config) {
      providers.push({ provide: INTERCEPTOR_CONFIG, useValue: config });
    }

    // Token Provider
    if (tokenProvider) {
      if (typeof tokenProvider === 'function') {
        providers.push({ provide: TOKEN_PROVIDER, useClass: tokenProvider });
      } else {
        providers.push({ provide: TOKEN_PROVIDER, useValue: tokenProvider });
      }
    }

    // Notification Service
    if (notificationService) {
      if (typeof notificationService === 'function') {
        providers.push({
          provide: ERROR_NOTIFICATION_SERVICE,
          useClass: notificationService,
        });
      } else {
        providers.push({
          provide: ERROR_NOTIFICATION_SERVICE,
          useValue: notificationService,
        });
      }
    }

    // Interceptors in execution order (CRITICAL!)
    const interceptorOrder = [
      { key: 'baseUrl', class: BaseUrlInterceptor },
      { key: 'auth', class: AuthInterceptor },
      { key: 'timeout', class: TimeoutInterceptor },
      { key: 'loading', class: LoadingInterceptor },
      { key: 'cache', class: CacheInterceptor },
      { key: 'retry', class: RetryInterceptor },
      { key: 'error', class: ErrorInterceptor },
    ];

    interceptorOrder.forEach(({ key, class: interceptorClass }) => {
      providers.push({
        provide: HTTP_INTERCEPTORS,
        useClass: interceptorClass,
        multi: true,
      });
      // Also provide as individual service for direct injection
      providers.push(interceptorClass);
    });

    return { ngModule: HttpModule, providers };
  }
}
