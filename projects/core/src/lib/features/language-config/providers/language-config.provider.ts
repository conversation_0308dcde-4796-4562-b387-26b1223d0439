import { Observable } from 'rxjs';
import { ILanguageConfig } from '../interfaces';
import { Provider, Type } from '@angular/core';
import { LANGUAGE_CONFIG_TOKEN } from '../constants';

// Abstract service for providing language config
export abstract class LanguageConfigProvider {
  abstract getLanguageConfig():
    | Observable<ILanguageConfig>
    | Promise<ILanguageConfig>
    | ILanguageConfig;
}

// Value-based provider
export function provideLanguageConfig(config: ILanguageConfig): Provider[] {
  return [{ provide: LANGUAGE_CONFIG_TOKEN, useValue: config }];
}

// Service-based provider
export function provideLanguageConfigService<T extends LanguageConfigProvider>(
  serviceClass: Type<T>
): Provider[] {
  return [
    serviceClass,
    {
      provide: LANGUAGE_CONFIG_TOKEN,
      useFactory: (service: T) => service.getLanguageConfig(),
      deps: [serviceClass],
    },
  ];
}

// Factory-based provider
export function provideLanguageConfigFactory(
  factory: () =>
    | ILanguageConfig
    | Promise<ILanguageConfig>
    | Observable<ILanguageConfig>,
  deps: any[] = []
): Provider[] {
  return [
    {
      provide: LANGUAGE_CONFIG_TOKEN,
      useFactory: factory,
      deps: deps,
    },
  ];
}
