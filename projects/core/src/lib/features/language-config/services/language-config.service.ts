import { Inject, Injectable, Optional } from '@angular/core';
import { LANGUAGE_CONFIG_TOKEN } from '../constants';
import { ILanguageConfig } from '../interfaces';

@Injectable()
export class LanguageConfigService {
  public constructor(
    @Optional()
    @Inject(LANGUAGE_CONFIG_TOKEN)
    private readonly languageConfig: ILanguageConfig | null
  ) {}

  public getLanguageConfig(): ILanguageConfig {
    if (!this.languageConfig) return this.getDefaultConfig();
    return this.languageConfig;
  }

  public hasLanguageConfig(): boolean {
    return this.languageConfig !== null;
  }

  public getCurrentLanguage(): string {
    const config = this.getLanguageConfig();
    return config.defaultLanguage;
  }

  private getDefaultConfig(): ILanguageConfig {
    return { defaultLanguage: 'en' };
  }
}
