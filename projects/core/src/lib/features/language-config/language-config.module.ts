import { NgModule, ModuleWithProviders, Type } from '@angular/core';
import { ILanguageConfig } from './interfaces';
import {
  provideLanguageConfig,
  provideLanguageConfigService,
  provideLanguageConfigFactory,
  LanguageConfigProvider,
} from './providers';
import { LanguageConfigService } from './services';

@NgModule({})
export class LanguageConfigModule {
  // Value-based configuration
  public static forRoot(
    config: ILanguageConfig
  ): ModuleWithProviders<LanguageConfigModule> {
    return {
      ngModule: LanguageConfigModule,
      providers: [...provideLanguageConfig(config), LanguageConfigService],
    };
  }

  // Service-based configuration
  public static forRootWithService<T extends LanguageConfigProvider>(
    serviceClass: Type<T>
  ): ModuleWithProviders<LanguageConfigModule> {
    return {
      ngModule: LanguageConfigModule,
      providers: [
        ...provideLanguageConfigService(serviceClass),
        LanguageConfigService,
      ],
    };
  }

  // Factory-based configuration
  public static forRootWithFactory(
    factory: () => ILanguageConfig | Promise<ILanguageConfig>,
    deps: any[] = []
  ): ModuleWithProviders<LanguageConfigModule> {
    return {
      ngModule: LanguageConfigModule,
      providers: [
        ...provideLanguageConfigFactory(factory, deps),
        LanguageConfigService,
      ],
    };
  }

  // For feature modules that don't need to configure language
  public static forChild(): ModuleWithProviders<LanguageConfigModule> {
    return {
      ngModule: LanguageConfigModule,
      providers: [], // Service is already provided in root
    };
  }
}
