<!-- Main dialog container with full height and basic styling -->
<div class="flex flex-col bg-white rounded shadow-md min-w-[300px] h-full">
  <!-- Header section with title and close button -->
  <div class="flex justify-between items-center p-4 border-b border-gray-200 shrink-0">
    <!-- Left side: Icon and title container -->
    <div class="flex items-center space-x-2">
      <!-- Dynamic icon rendering based on icon type -->
      <ng-container *ngIf="data.icon">
        <i *ngIf="data.icon.iconClass" [class]="data.icon.iconClass"></i>
        <span *ngIf="data.icon.customIcon" [innerHTML]="data.icon.customIcon | sanitize"></span>
      </ng-container>
      <h2 class="m-0 text-base font-medium">{{ data.title }}</h2>
    </div>
    <!-- Close button with hover effect -->
    <button class="p-1 flex items-center justify-center rounded bg-transparent border-none cursor-pointer hover:bg-gray-100 transition-colors duration-200" (click)="close()">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>
  <!-- Content area with flex grow and scroll capability -->
  <div class="flex-1 p-4 overflow-auto">
    <ng-container #contentContainer></ng-container>
  </div>
</div>
