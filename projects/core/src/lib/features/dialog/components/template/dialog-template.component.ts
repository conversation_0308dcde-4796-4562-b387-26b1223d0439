import {
  Component,
  ComponentFactoryResolver,
  ComponentRef,
  Inject,
  Injector,
  OnDestroy,
  OnInit,
  Type,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { DIALOG_DATA, DIALOG_REF } from '../../constants/tokens.constant';
import { IDialogRef } from '../../interfaces/dialog-ref.interface';
import { IDialogIcon } from '../../interfaces/dialog-icon.interface';

interface IDialogTemplateData {
  title: string;
  icon?: IDialogIcon;
  component: Type<any>;
  componentData?: any;
}

@Component({
  selector: 'ng-omar-dialog-template',
  templateUrl: './dialog-template.component.html',
  styles: [
    `
      :host {
        display: block;
        width: 100%;
        height: 100%;
      }
    `,
  ],
})
export class DialogTemplateComponent implements OnInit, OnDestroy {
  @ViewChild('contentContainer', { read: ViewContainerRef, static: true })
  public readonly contentContainer!: ViewContainerRef;

  private componentRef?: ComponentRef<any>;

  constructor(
    @Inject(DIALOG_DATA) public readonly data: IDialogTemplateData,
    private readonly componentFactoryResolver: ComponentFactoryResolver,
    private readonly injector: Injector,
    @Inject(DIALOG_REF) private readonly dialogRef: IDialogRef
  ) {}

  public ngOnInit() {
    this.loadComponent();
  }

  public ngOnDestroy() {
    if (this.componentRef) {
      this.componentRef.destroy();
    }
  }

  public close() {
    this.dialogRef.close();
  }

  private loadComponent() {
    this.contentContainer.clear();

    const componentFactory =
      this.componentFactoryResolver.resolveComponentFactory(
        this.data.component
      );

    this.componentRef = this.contentContainer.createComponent(
      componentFactory,
      0,
      this.injector
    );

    if (this.componentRef && this.data.componentData) {
      Object.assign(this.componentRef.instance, this.data.componentData);
    }
  }
}
