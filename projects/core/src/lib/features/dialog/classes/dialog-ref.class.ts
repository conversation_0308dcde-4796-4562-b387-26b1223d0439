import { Subject, Observable } from 'rxjs';
import { IDialogRef } from '../interfaces/dialog-ref.interface';

/**
 * Reference to a dialog opened via the Dialog service.
 */
export class DialogRef<T = any, R = any> implements IDialogRef<T, R> {
  /** Subject for when the dialog has opened */
  private readonly _afterOpened = new Subject<void>();

  /** Subject for the dialog result */
  private readonly _afterClosed = new Subject<R | undefined>();

  /** Subject for before the dialog closes */
  private readonly _beforeClosed = new Subject<R | undefined>();

  /** Result to be passed when closing */
  private _result: R | undefined;

  /** Whether the dialog is closed */
  private _closed = false;

  /** The instance of component opened into the dialog */
  public componentInstance: T | null = null;

  /** Unique ID for this dialog */
  public readonly id: string;

  /**
   * Check if dialog is closed
   */
  public get isClosed(): boolean {
    return this._closed;
  }

  /**
   * Get the result value
   */
  public get result(): R | undefined {
    return this._result;
  }

  public constructor(id: string) {
    this.id = id;
  }

  /**
   * Close the dialog.
   * @param result Optional result to return to the dialog opener.
   */
  public close(result?: R): void {
    if (this._closed) {
      return;
    }

    this._result = result;
    this._closed = true;

    // Emit before close event
    this._beforeClosed.next(result);
    this._beforeClosed.complete();

    // Emit after close event
    this._afterClosed.next(result);
    this._afterClosed.complete();

    // Complete other subjects
    this._afterOpened.complete();
  }

  /**
   * Gets an observable that is notified when the dialog is finished opening.
   */
  public afterOpened(): Observable<void> {
    return this._afterOpened.asObservable();
  }

  /**
   * Gets an observable that is notified when the dialog is finished closing.
   */
  public afterClosed(): Observable<R | undefined> {
    return this._afterClosed.asObservable();
  }

  /**
   * Gets an observable that is notified when the dialog has started closing.
   */
  public beforeClosed(): Observable<R | undefined> {
    return this._beforeClosed.asObservable();
  }

  /**
   * Notify that the dialog has finished opening.
   * @internal
   */
  public _notifyOpenComplete(): void {
    this._afterOpened.next();
    this._afterOpened.complete();
  }
}
