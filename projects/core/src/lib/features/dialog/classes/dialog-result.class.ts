export class DialogResult<T = any> {
  public constructor(
    public readonly value: T,
    public readonly action?: 'confirm' | 'cancel' | 'close' | string
  ) {}

  /** Create a confirm result */
  public static confirm<T>(value?: T): DialogResult<T> {
    return new DialogResult(value!, 'confirm');
  }

  /** Create a cancel result */
  public static cancel<T>(value?: T): DialogResult<T> {
    return new DialogResult(value!, 'cancel');
  }

  /** Create a close result */
  public static close<T>(value?: T): DialogResult<T> {
    return new DialogResult(value!, 'close');
  }

  /** Check if result is a confirmation */
  public get isConfirmed(): boolean {
    return this.action === 'confirm';
  }

  /** Check if result is a cancellation */
  public get isCancelled(): boolean {
    return this.action === 'cancel';
  }
}
