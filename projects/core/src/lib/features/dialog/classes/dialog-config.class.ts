import { IDialogConfig } from '../interfaces';

export class DialogConfig<T = any> implements IDialogConfig<T> {
  /** Data being injected into the dialog */
  public data?: T;

  /** Width of the dialog */
  public width?: string;

  /** Height of the dialog */
  public height?: string;

  /** Whether the dialog has a backdrop */
  public hasBackdrop?: boolean;

  /** Whether clicking backdrop closes dialog */
  public closeOnBackdropClick?: boolean;

  /** Whether ESC key closes dialog */
  public closeOnEscape?: boolean;

  /** Custom panel class */
  public panelClass?: string | string[];

  /** Custom backdrop class */
  public backdropClass?: string | string[];

  public constructor(config?: Partial<IDialogConfig<T>>) {
    if (config) {
      Object.assign(this, config);
    }
  }

  /**
   * Merge this config with another config object
   */
  public merge(config?: Partial<IDialogConfig<T>>): DialogConfig<T> {
    return new DialogConfig({ ...this, ...config });
  }

  /**
   * Apply default values to undefined properties
   */
  public applyDefaults(defaults: Partial<IDialogConfig<T>>): DialogConfig<T> {
    const result = new DialogConfig<T>(this);

    Object.keys(defaults).forEach((key) => {
      if (result[key as keyof DialogConfig<T>] === undefined) {
        (result as any)[key] = (defaults as any)[key];
      }
    });

    return result;
  }

  /**
   * Convert panelClass to array if it's a string
   */
  public getPanelClasses(): string[] {
    if (!this.panelClass) return [];
    return typeof this.panelClass === 'string'
      ? this.panelClass.split(' ').filter((c) => c.length > 0)
      : this.panelClass;
  }

  /**
   * Convert backdropClass to array if it's a string
   */
  public getBackdropClasses(): string[] {
    if (!this.backdropClass) return [];
    return typeof this.backdropClass === 'string'
      ? this.backdropClass.split(' ').filter((c) => c.length > 0)
      : this.backdropClass;
  }
}
