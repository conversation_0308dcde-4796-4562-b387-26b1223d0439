import { Subject } from 'rxjs';
import { DialogState } from '../enums';

export class DialogStateModel {
  private _state: DialogState = DialogState.CLOSED;
  private readonly _stateChanges = new Subject<DialogState>();

  /** Observable of state changes */
  public readonly stateChanges$ = this._stateChanges.asObservable();

  /** Get current state */
  public get state(): DialogState {
    return this._state;
  }

  /** Set state and notify observers */
  public setState(state: DialogState): void {
    if (this._state !== state) {
      this._state = state;
      this._stateChanges.next(state);

      if (state === DialogState.CLOSED) {
        this._stateChanges.complete();
      }
    }
  }

  /** Check if dialog is open */
  public get isOpen(): boolean {
    return this._state === DialogState.OPEN;
  }

  /** Check if dialog is closed */
  public get isClosed(): boolean {
    return this._state === DialogState.CLOSED;
  }

  /** Check if dialog is animating */
  public get isAnimating(): boolean {
    return (
      this._state === DialogState.OPENING || this._state === DialogState.CLOSING
    );
  }
}
