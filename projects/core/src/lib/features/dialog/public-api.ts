// Module
export * from './dialog.module';

// Main Service
export * from './services/dialog.service';

// Components
export * from './components';

// Interfaces
export * from './interfaces/dialog-config.interface';
export * from './interfaces/dialog-ref.interface';
export * from './interfaces/dialog-container.interface';
export * from './interfaces/dialog-service.interface';
export * from './interfaces/dialog-template-config.interface';

// Template Service
export * from './services/dialog-template.service';

// Classes
export * from './classes/dialog-ref.class';
export * from './classes/dialog-config.class';
export * from './classes/dialog-result.class';

// Enums
export * from './enums/dialog-state.enum';

// Tokens (for advanced users)
export {
  DIALOG_DATA,
  DIALOG_REF,
  DIALOG_CONFIG,
  DEFAULT_DIALOG_CONFIG,
} from './constants/tokens.constant';
