import { Injectable, Injector, InjectFlags } from '@angular/core';
import { DialogConfig } from '../classes/dialog-config.class';
import { DialogRef } from '../classes/dialog-ref.class';
import { DIALOG_REF, DIALOG_DATA, DIALOG_CONFIG } from '../constants';

/**
 * Custom injector to be used when providing custom injection tokens to dialog components.
 */
@Injectable()
export class DialogInjector implements Injector {
  constructor(
    private readonly parentInjector: Injector,
    private readonly additionalTokens: WeakMap<any, any>
  ) {}

  public get<T>(token: any, notFoundValue?: T, flags?: InjectFlags): T {
    const value = this.additionalTokens.get(token);

    if (value !== undefined) return value;

    return this.parentInjector.get<T>(token, notFoundValue, flags);
  }

  /**
   * Create a custom injector for dialog component
   */
  public static create(
    parentInjector: Injector,
    dialogRef: DialogRef,
    config: DialogConfig
  ): DialogInjector {
    const map = new WeakMap<any, any>();

    map.set(DIALOG_REF, dialogRef);
    map.set(DIALOG_DATA, config.data);
    map.set(DIALOG_CONFIG, config);

    return new DialogInjector(parentInjector, map);
  }
}
