import { Injectable } from '@angular/core';
import { DialogService } from './dialog.service';
import { IDialogRef } from '../interfaces/dialog-ref.interface';
import { IDialogTemplateConfig } from '../interfaces/dialog-template-config.interface';
import { DialogTemplateComponent } from '../components/template/dialog-template.component';

@Injectable()
export class DialogTemplateService {
  public constructor(private readonly dialogService: DialogService) {}

  /**
   * Opens a dialog with the template layout containing the specified component
   * @param config The configuration for the dialog template
   * @returns A reference to the created dialog
   */
  public open<T>(config: IDialogTemplateConfig<T>): IDialogRef<T> {
    const dialogRef = this.dialogService.open<DialogTemplateComponent>(
      DialogTemplateComponent,
      {
        data: {
          title: config.title,
          icon: config.icon,
          component: config.component,
          componentData: config.componentData,
        },
        width: config.width,
        height: config.height,
        panelClass:
          typeof config.cssClass === 'string'
            ? [config.cssClass]
            : config.cssClass,
      }
    );

    return dialogRef as unknown as IDialogRef<T>;
  }
}
