import {
  Injectable,
  ComponentFactoryResolver,
  ApplicationRef,
  Injector,
  Inject,
  Type,
  ComponentRef,
  EmbeddedViewRef,
} from '@angular/core';
import { fromEvent, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { DialogConfig } from '../classes/dialog-config.class';
import { DialogRef } from '../classes/dialog-ref.class';
import { DialogStateModel } from '../classes/dialog-state.class';
import { DEFAULT_DIALOG_CONFIG, DIALOG_ID_GENERATOR } from '../constants';
import { DialogState } from '../enums';
import { IDialogService, IDialogConfig, IDialogRef } from '../interfaces';
import { DialogDomService } from './dialog-dom.service';
import { DialogInjector } from './dialog-injector.service';
import { DialogManagerService } from './dialog-manager.service';
import { DialogStylesService } from './dialog-styles.service';

/**
 * Service to open modal dialogs
 */
@Injectable()
export class DialogService implements IDialogService {
  // FIX 1: Track if we already have a backdrop
  private hasGlobalBackdrop = false;
  private globalBackdropElement: HTMLElement | null = null;
  private backdropRefCount = 0;

  // FIX 2: Track ESC key subscriptions per dialog
  private readonly escapeKeySubscriptions = new Map<string, Subscription>();

  constructor(
    private readonly componentFactoryResolver: ComponentFactoryResolver,
    private readonly appRef: ApplicationRef,
    private readonly injector: Injector,
    private readonly domService: DialogDomService,
    private readonly managerService: DialogManagerService,
    private readonly stylesService: DialogStylesService,
    @Inject(DEFAULT_DIALOG_CONFIG)
    private readonly defaultConfig: Partial<IDialogConfig>,
    @Inject(DIALOG_ID_GENERATOR) private readonly idGenerator: () => string
  ) {
    this.stylesService.injectStyles();
  }

  /**
   * Opens a modal dialog containing the given component.
   */
  public open<T, D = any, R = any>(
    component: Type<T>,
    config?: IDialogConfig<D>
  ): IDialogRef<T, R> {
    // Merge configurations
    const dialogConfig = this.mergeConfig(config);

    // Generate unique ID
    const dialogId = this.idGenerator();

    // Create dialog reference
    const dialogRef = new DialogRef<T, R>(dialogId);

    // Create dialog state
    const dialogState = new DialogStateModel();

    // FIX 1: Handle backdrop - only create one shared backdrop
    if (dialogConfig.hasBackdrop) {
      this.handleBackdrop(dialogConfig);
    }

    // Create dialog wrapper
    const dialogWrapper = this.createDialogWrapper(dialogConfig, dialogRef);

    // Create component
    const componentRef = this.createComponent(
      component,
      dialogWrapper,
      dialogRef,
      dialogConfig
    );

    // Set component instance on dialog ref
    dialogRef.componentInstance = componentRef.instance;

    // Add to manager
    this.managerService.add(dialogRef);

    // FIX 2: Setup close handlers with proper isolation
    this.setupCloseHandlers(dialogRef, dialogConfig, dialogWrapper);

    // Handle cleanup on close
    dialogRef.afterClosed().subscribe(() => {
      this.cleanup(componentRef, dialogWrapper, dialogRef.id, dialogConfig);
    });

    // Append to DOM
    this.domService.appendToBody(dialogWrapper);

    // Set state to open
    dialogState.setState(DialogState.OPEN);

    // Notify that dialog has opened
    setTimeout(() => {
      dialogRef._notifyOpenComplete();
      this.domService.trapFocus(dialogWrapper);
    }, 100);

    return dialogRef;
  }

  /**
   * FIX 1: Handle shared backdrop for multiple dialogs
   */
  private handleBackdrop(config: DialogConfig): void {
    this.backdropRefCount++;

    if (!this.hasGlobalBackdrop) {
      // Create the backdrop only once
      this.globalBackdropElement = this.domService.createBackdrop(
        config.getBackdropClasses()
      );
      this.domService.appendToBody(this.globalBackdropElement);
      this.hasGlobalBackdrop = true;

      // Setup backdrop click handler
      if (config.closeOnBackdropClick) {
        fromEvent(this.globalBackdropElement, 'click').subscribe(() => {
          // Close only the top-most dialog when backdrop is clicked
          const topDialog = this.managerService.getTopDialog();
          if (topDialog) {
            topDialog.close();
          }
        });
      }
    }
  }

  /**
   * FIX 1: Remove backdrop only when no dialogs are open
   */
  private removeBackdropIfNeeded(): void {
    this.backdropRefCount--;

    if (this.backdropRefCount === 0 && this.globalBackdropElement) {
      this.domService.removeFromDom(this.globalBackdropElement);
      this.globalBackdropElement = null;
      this.hasGlobalBackdrop = false;
    }
  }

  /**
   * Closes all currently-open dialogs.
   */
  public closeAll(): void {
    this.managerService.closeAll();
  }

  /**
   * Gets all open dialog references.
   */
  public getOpenDialogs(): IDialogRef[] {
    return this.managerService.getOpenDialogs();
  }

  /**
   * Finds an open dialog by its id.
   */
  public getDialogById(id: string): IDialogRef | undefined {
    return this.managerService.getDialogById(id);
  }

  /**
   * Merge user config with default config
   */
  private mergeConfig<D>(config?: IDialogConfig<D>): DialogConfig<D> {
    const userConfig = new DialogConfig(config);
    return userConfig.applyDefaults(this.defaultConfig);
  }

  /**
   * Create dialog wrapper element
   */
  private createDialogWrapper(
    config: DialogConfig,
    dialogRef: DialogRef
  ): HTMLElement {
    const wrapper = this.domService.createDialogWrapper(
      config.getPanelClasses()
    );

    // Apply size
    this.domService.applySize(wrapper, config.width, config.height);

    // Set z-index
    const zIndex = this.managerService.getNextZIndex();
    wrapper.style.zIndex = zIndex.toString();

    // Store dialog ID on element for identification
    wrapper.setAttribute('data-dialog-id', dialogRef.id);

    return wrapper;
  }

  /**
   * Create and attach component to dialog
   */
  private createComponent<T>(
    component: Type<T>,
    container: HTMLElement,
    dialogRef: DialogRef,
    config: DialogConfig
  ): ComponentRef<T> {
    // Create custom injector
    const injector = DialogInjector.create(this.injector, dialogRef, config);

    // Create component factory
    const factory =
      this.componentFactoryResolver.resolveComponentFactory(component);

    // Create component with custom injector
    const componentRef = factory.create(injector);

    // Attach component to application
    this.appRef.attachView(componentRef.hostView);

    // Get DOM element and append to container
    const domElem = (componentRef.hostView as EmbeddedViewRef<any>)
      .rootNodes[0] as HTMLElement;
    container.appendChild(domElem);

    // Detect changes
    componentRef.changeDetectorRef.detectChanges();

    return componentRef;
  }

  /**
   * FIX 2: Setup handlers for closing the dialog with proper isolation
   */
  private setupCloseHandlers(
    dialogRef: DialogRef,
    config: DialogConfig,
    wrapper: HTMLElement
  ): void {
    // ESC key handler - only close the top-most dialog
    if (config.closeOnEscape) {
      const escSubscription = fromEvent<KeyboardEvent>(document, 'keydown')
        .pipe(
          filter((event) => {
            // Only handle ESC if this dialog is the top-most one
            const topDialog = this.managerService.getTopDialog();
            return event.key === 'Escape' && topDialog?.id === dialogRef.id;
          })
        )
        .subscribe(() => dialogRef.close());

      // Store subscription for cleanup
      this.escapeKeySubscriptions.set(dialogRef.id, escSubscription);
    }
  }

  /**
   * Clean up after dialog closes
   */
  private cleanup(
    componentRef: ComponentRef<any>,
    wrapper: HTMLElement,
    dialogId: string,
    config: DialogConfig
  ): void {
    // Destroy component
    this.appRef.detachView(componentRef.hostView);
    componentRef.destroy();

    // Remove DOM elements
    this.domService.removeFromDom(wrapper);

    // FIX 1: Handle backdrop removal
    if (config.hasBackdrop) {
      this.removeBackdropIfNeeded();
    }

    // FIX 2: Clean up ESC key subscription for this specific dialog
    const escSubscription = this.escapeKeySubscriptions.get(dialogId);
    if (escSubscription) {
      escSubscription.unsubscribe();
      this.escapeKeySubscriptions.delete(dialogId);
    }
  }
}
