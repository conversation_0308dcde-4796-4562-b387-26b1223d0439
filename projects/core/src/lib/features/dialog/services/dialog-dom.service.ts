import { Injectable, Inject } from '@angular/core';
import { DIALOG_DOCUMENT } from '../constants';

/**
 * Service for DOM manipulation operations
 */
@Injectable()
export class DialogDomService {
  private dialogCounter = 0;
  private dialogContainer: HTMLElement | null = null;

  constructor(@Inject(DIALOG_DOCUMENT) private readonly document: Document) {}

  /**
   * Create backdrop element
   */
  public createBackdrop(classes: string[] = []): HTMLElement {
    const backdrop = this.document.createElement('div');
    backdrop.classList.add('dialog-backdrop', ...classes);
    backdrop.setAttribute('aria-hidden', 'true');

    // Default styles
    backdrop.style.position = 'fixed';
    backdrop.style.top = '0';
    backdrop.style.left = '0';
    backdrop.style.right = '0';
    backdrop.style.bottom = '0';
    backdrop.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    backdrop.style.zIndex = '1000';

    return backdrop;
  }

  /**
   * Create dialog wrapper element
   */
  public createDialogWrapper(classes: string[] = []): HTMLElement {
    const wrapper = this.document.createElement('div');
    wrapper.classList.add('dialog-wrapper', ...classes);
    wrapper.setAttribute('role', 'dialog');
    wrapper.setAttribute('aria-modal', 'true');
    wrapper.tabIndex = -1;

    // Default styles
    wrapper.style.position = 'fixed';
    wrapper.style.top = '50%';
    wrapper.style.left = '50%';
    wrapper.style.transform = 'translate(-50%, -50%)';
    wrapper.style.backgroundColor = 'white';
    wrapper.style.borderRadius = '4px';
    wrapper.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    wrapper.style.zIndex = '1001';
    wrapper.style.maxWidth = '90vw';
    wrapper.style.maxHeight = '90vh';
    wrapper.style.overflow = 'auto';

    return wrapper;
  }

  /**
   * Apply size styles to element
   */
  public applySize(
    element: HTMLElement,
    width?: string,
    height?: string
  ): void {
    if (width) element.style.width = width;
    if (height) element.style.height = height;
  }

  /**
   * Append element to body
   */
  public appendToBody(element: HTMLElement): void {
    this.document.body.appendChild(element);
  }

  /**
   * Remove element from DOM
   */
  public removeFromDom(element: HTMLElement): void {
    if (element?.parentNode) {
      element.parentNode.removeChild(element);
    }
  }

  /**
   * Get or create dialog container
   */
  public getDialogContainer(): HTMLElement {
    if (!this.dialogContainer) {
      this.dialogContainer = this.document.createElement('div');
      this.dialogContainer.classList.add('dialog-container-root');
      this.dialogContainer.style.position = 'fixed';
      this.dialogContainer.style.top = '0';
      this.dialogContainer.style.left = '0';
      this.dialogContainer.style.width = '0';
      this.dialogContainer.style.height = '0';
      this.dialogContainer.style.zIndex = '999';
      this.appendToBody(this.dialogContainer);
    }
    return this.dialogContainer;
  }

  /**
   * Generate unique dialog ID
   */
  public generateDialogId(): string {
    return `dialog-${++this.dialogCounter}`;
  }

  /**
   * Add classes to element
   */
  public addClasses(element: HTMLElement, classes: string[]): void {
    classes.forEach((className) => {
      if (className?.trim()) {
        element.classList.add(className.trim());
      }
    });
  }

  /**
   * Remove classes from element
   */
  public removeClasses(element: HTMLElement, classes: string[]): void {
    classes.forEach((className) => {
      if (className?.trim()) {
        element.classList.remove(className.trim());
      }
    });
  }

  /**
   * Focus trap within element
   */
  public trapFocus(element: HTMLElement): () => void {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
    ];

    const focusableElements = element.querySelectorAll<HTMLElement>(
      focusableSelectors.join(', ')
    );

    if (focusableElements.length === 0) {
      element.tabIndex = -1;
      element.focus();
      return () => {};
    }

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        if (this.document.activeElement === firstElement) {
          lastElement.focus();
          event.preventDefault();
        }
      } else if (this.document.activeElement === lastElement) {
        firstElement.focus();
        event.preventDefault();
      }
    };

    element.addEventListener('keydown', handleKeyDown);
    firstElement.focus();

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
    };
  }
}
