import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { DialogRef } from '../classes/dialog-ref.class';

/**
 * Service to manage all open dialogs
 */
@Injectable()
export class DialogManagerService {
  private readonly openDialogs: Map<string, DialogRef> = new Map();
  private readonly dialogStack: string[] = []; // Track order of dialogs
  private readonly afterAllClosed = new Subject<void>();
  private currentZIndex = 1000;

  public readonly afterAllClosed$ = this.afterAllClosed.asObservable();

  /**
   * Add a dialog to the manager
   */
  public add(dialogRef: DialogRef): void {
    this.openDialogs.set(dialogRef.id, dialogRef);
    this.dialogStack.push(dialogRef.id); // Track order

    // Clean up when dialog closes
    dialogRef.afterClosed().subscribe(() => {
      this.remove(dialogRef.id);
    });
  }

  /**
   * Remove a dialog from the manager
   */
  public remove(id: string): void {
    this.openDialogs.delete(id);

    // Remove from stack
    const index = this.dialogStack.indexOf(id);
    if (index > -1) {
      this.dialogStack.splice(index, 1);
    }

    if (this.openDialogs.size === 0) {
      this.afterAllClosed.next();
      this.resetZIndex();
    }
  }

  /**
   * Get a dialog by ID
   */
  public getDialogById(id: string): DialogRef | undefined {
    return this.openDialogs.get(id);
  }

  /**
   * Get all open dialogs
   */
  public getOpenDialogs(): DialogRef[] {
    return Array.from(this.openDialogs.values());
  }

  /**
   * Close all open dialogs
   */
  public closeAll(): void {
    // Close in reverse order (top to bottom)
    [...this.dialogStack].reverse().forEach((id) => {
      const dialog = this.openDialogs.get(id);
      if (dialog) {
        dialog.close();
      }
    });
  }

  /**
   * Check if there are open dialogs
   */
  public hasOpenDialogs(): boolean {
    return this.openDialogs.size > 0;
  }

  /**
   * Get next z-index for stacking
   */
  public getNextZIndex(): number {
    return this.currentZIndex++;
  }

  /**
   * Reset z-index counter
   */
  public resetZIndex(): void {
    this.currentZIndex = 1000;
  }

  /**
   * Get the top-most dialog (last in stack)
   */
  public getTopDialog(): DialogRef | undefined {
    if (this.dialogStack.length === 0) return undefined;

    const topId = this.dialogStack[this.dialogStack.length - 1];
    return this.openDialogs.get(topId);
  }
}
