import { Injectable, Inject } from '@angular/core';
import { DIALOG_DOCUMENT } from '../constants';

@Injectable()
export class DialogStylesService {
  private stylesInjected = false;
  private styleElement: HTMLStyleElement | null = null;

  constructor(@Inject(DIALOG_DOCUMENT) private readonly document: Document) {}

  /**
   * Inject dialog styles into the document head
   */
  public injectStyles(): void {
    if (this.stylesInjected) return;

    const styles = this.getStyles();

    this.styleElement = this.document.createElement('style');
    this.styleElement.setAttribute('data-dialog-styles', 'true');
    this.styleElement.textContent = styles;
    this.document.head.appendChild(this.styleElement);

    this.stylesInjected = true;
  }

  /**
   * Remove injected styles (for cleanup if needed)
   */
  public removeStyles(): void {
    if (this.styleElement?.parentNode) {
      this.styleElement.parentNode.removeChild(this.styleElement);
      this.styleElement = null;
      this.stylesInjected = false;
    }
  }

  /**
   * Get the CSS styles as a string
   */
  private getStyles(): string {
    return `
      /* Dialog Backdrop Styles */
      .dialog-backdrop {
        animation: dialog-fadeIn 0.3s ease-in-out;
      }
      
      .dialog-backdrop.dialog-closing {
        animation: dialog-fadeOut 0.3s ease-in-out;
      }
      
      /* Dialog Wrapper Styles */
      .dialog-wrapper {
        animation: dialog-slideIn 0.3s ease-in-out;
        display: flex;
        flex-direction: column;
      }
      
      .dialog-wrapper.dialog-closing {
        animation: dialog-slideOut 0.3s ease-in-out;
      }
      
      /* Dialog Content Sections */
      .dialog-wrapper .dialog-content {
        flex: 1;
        overflow: auto;
        padding: 20px;
      }
      
      .dialog-wrapper .dialog-header {
        padding: 20px;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      
      .dialog-wrapper .dialog-header h1,
      .dialog-wrapper .dialog-header h2,
      .dialog-wrapper .dialog-header h3 {
        margin: 0;
      }
      
      .dialog-wrapper .dialog-actions {
        padding: 16px 20px;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }
      
      .dialog-wrapper .dialog-actions button {
        min-width: 80px;
      }
      
      /* Close Button */
      .dialog-close-button {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 0;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: background-color 0.2s;
      }
      
      .dialog-close-button:hover {
        background-color: #f0f0f0;
      }
      
      /* Animations */
      @keyframes dialog-fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      @keyframes dialog-fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
      
      @keyframes dialog-slideIn {
        from {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
      
      @keyframes dialog-slideOut {
        from {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
        to {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.9);
        }
      }
      
      /* Responsive Design */
      @media (max-width: 600px) {
        .dialog-wrapper {
          width: 100% !important;
          max-width: 100vw !important;
          height: 100vh !important;
          max-height: 100vh !important;
          margin: 0 !important;
          border-radius: 0 !important;
          top: 0 !important;
          left: 0 !important;
          transform: none !important;
          animation: dialog-slideUp 0.3s ease-in-out;
        }
        
        .dialog-wrapper.dialog-closing {
          animation: dialog-slideDown 0.3s ease-in-out;
        }
        
        @keyframes dialog-slideUp {
          from {
            transform: translateY(100%);
          }
          to {
            transform: translateY(0);
          }
        }
        
        @keyframes dialog-slideDown {
          from {
            transform: translateY(0);
          }
          to {
            transform: translateY(100%);
          }
        }
      }
    `;
  }
}
