// dialog.tokens.ts
import { InjectionToken } from '@angular/core';
import { IDialogConfig, IDialogRef } from '../interfaces';

/**
 * Injection token for the dialog data passed into a dialog component
 * Usage: constructor(@Inject(DIALOG_DATA) public data: any) {}
 */
export const DIALOG_DATA = new InjectionToken<any>('DIALOG_DATA');

/**
 * Injection token for the dialog configuration
 * Usage: constructor(@Inject(DIALOG_CONFIG) public config: IDialogConfig) {}
 */
export const DIALOG_CONFIG = new InjectionToken<IDialogConfig>('DIALOG_CONFIG');

/**
 * Injection token for the dialog reference
 * Usage: constructor(@Inject(DIALOG_REF) public dialogRef: IDialogRef) {}
 */
export const DIALOG_REF = new InjectionToken<IDialogRef>('DIALOG_REF');

/**
 * Injection token for the default dialog configuration
 * This can be provided at module level to set default config for all dialogs
 */
export const DEFAULT_DIALOG_CONFIG = new InjectionToken<Partial<IDialogConfig>>(
  'DEFAULT_DIALOG_CONFIG',
  {
    providedIn: 'root',
    factory: () => ({
      width: '500px',
      hasBackdrop: true,
      closeOnBackdropClick: true,
      closeOnEscape: true,
      panelClass: '',
      backdropClass: '',
    }),
  }
);

/**
 * Injection token for the dialog container
 * Used internally by the dialog service
 */
export const DIALOG_CONTAINER = new InjectionToken<any>('DIALOG_CONTAINER');

/**
 * Injection token for unique dialog ID generator
 * Can be overridden to provide custom ID generation logic
 */
export const DIALOG_ID_GENERATOR = new InjectionToken<() => string>(
  'DIALOG_ID_GENERATOR',
  {
    providedIn: 'root',
    factory: () => {
      let id = 0;
      return () => `dialog-${++id}`;
    },
  }
);

/**
 * Injection token for dialog scroll strategy
 * Defines how the page behaves when dialog is open
 */
export const DIALOG_SCROLL_STRATEGY = new InjectionToken<() => any>(
  'DIALOG_SCROLL_STRATEGY'
);

/**
 * Injection token for the document object
 * Useful for SSR compatibility
 */
export const DIALOG_DOCUMENT = new InjectionToken<Document>('DIALOG_DOCUMENT', {
  providedIn: 'root',
  factory: () => document,
});
