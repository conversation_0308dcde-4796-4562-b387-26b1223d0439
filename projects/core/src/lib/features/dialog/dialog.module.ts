import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DEFAULT_DIALOG_CONFIG,
  DIALOG_ID_GENERATOR,
  DIALOG_DOCUMENT,
} from './constants';
import { DialogTemplateComponent } from './components/template/dialog-template.component';
import { IDialogConfig } from './interfaces';
import {
  DialogService,
  DialogDomService,
  DialogManagerService,
  DialogInjector,
  DialogStylesService,
  DialogTemplateService,
} from './services';
import { CoreModule } from '../../core.module';

@NgModule({
  declarations: [DialogTemplateComponent],
  imports: [CommonModule, CoreModule],
  exports: [DialogTemplateComponent],
})
export class DialogModule {
  /**
   * Import DialogModule in the root module of your application.
   * This method allows you to configure default dialog options.
   */
  public static forRoot(
    config?: Partial<IDialogConfig>
  ): ModuleWithProviders<DialogModule> {
    return {
      ngModule: DialogModule,
      providers: [
        DialogStylesService,
        DialogService,
        DialogDomService,
        DialogManagerService,
        DialogInjector,
        DialogTemplateService,
        {
          provide: DEFAULT_DIALOG_CONFIG,
          useValue: {
            width: '500px',
            hasBackdrop: true,
            closeOnBackdropClick: true,
            closeOnEscape: true,
            panelClass: '',
            backdropClass: '',
            ...config,
          },
        },
        {
          provide: DIALOG_ID_GENERATOR,
          useFactory: () => {
            let id = 0;
            return () => `dialog-${++id}`;
          },
        },
        {
          provide: DIALOG_DOCUMENT,
          useFactory: () => document,
        },
      ],
    };
  }

  /**
   * Import DialogModule in child modules.
   * No providers are registered.
   */
  public static forChild(): ModuleWithProviders<DialogModule> {
    return {
      ngModule: DialogModule,
      providers: [],
    };
  }
}
