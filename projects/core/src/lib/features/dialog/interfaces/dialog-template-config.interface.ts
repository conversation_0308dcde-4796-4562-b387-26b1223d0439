import { Type } from '@angular/core';
import { IDialogIcon } from './dialog-icon.interface';

export interface IDialogTemplateConfig<T = any> {
  /**
   * Dialog title to be displayed in the header
   */
  title: string;

  /**
   * Optional icon class name for the header
   */
  icon?: IDialogIcon;

  /**
   * The component to be rendered in the dialog content area
   */
  component: Type<T>;

  /**
   * Optional data to be passed to the component
   */
  componentData?: any;

  /**
   * Optional dialog width (e.g., '500px', '50%')
   */
  width?: string;

  /**
   * Optional dialog height (e.g., '400px', '80vh')
   */
  height?: string;

  /**
   * Optional CSS classes to be added to the dialog container
   */
  cssClass?: string | string[];
}
