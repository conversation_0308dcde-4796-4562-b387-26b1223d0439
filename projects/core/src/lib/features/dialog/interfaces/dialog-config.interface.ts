export interface IDialogConfig<T = any> {
  /** Data to be injected into the dialog component */
  data?: T;

  /** Dialog width (e.g., '500px', '80%') */
  width?: string;

  /** Dialog height (e.g., '400px', '50%') */
  height?: string;

  /** Whether the dialog has a backdrop */
  hasBackdrop?: boolean;

  /** Whether clicking on the backdrop closes the dialog */
  closeOnBackdropClick?: boolean;

  /** Whether pressing ESC closes the dialog */
  closeOnEscape?: boolean;

  /** Custom CSS class(es) to apply to the dialog container */
  panelClass?: string | string[];

  /** Custom CSS class(es) to apply to the backdrop */
  backdropClass?: string | string[];
}
