import { ModuleWithProviders, NgModule } from '@angular/core';
import { CoreComponent } from './core.component';
import {
  MultiLanguageTextPipe,
  RandomNumberPipe,
  RangePipe,
  SanitizePipe,
} from './pipes';
import { ClickOutsideDirective } from './directives';

const SHARED_PIPES = [
  SanitizePipe,
  ClickOutsideDirective,
  RangePipe,
  RandomNumberPipe,
  MultiLanguageTextPipe,
];

@NgModule({
  declarations: [CoreComponent, SHARED_PIPES],
  imports: [],
  exports: [CoreComponent, SHARED_PIPES],
})
export class CoreModule {
  public static forRoot(): ModuleWithProviders<CoreModule> {
    return {
      ngModule: CoreModule,
      providers: [],
    };
  }
}
