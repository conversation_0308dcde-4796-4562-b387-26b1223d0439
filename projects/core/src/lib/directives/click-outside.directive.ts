import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
} from '@angular/core';

@Directive({ selector: '[clickOutside]' })
export class ClickOutsideDirective implements OnInit, OnChanges, OnDestroy {
  @Input() public clickOutsideEnabled: boolean = true;
  @Input() public clickOutsideElements?: (ElementRef | HTMLElement)[];

  @Output() public clickOutside = new EventEmitter<Event>();

  private clickListener?: () => void;

  public constructor(
    private readonly elementRef: ElementRef,
    private readonly renderer: Renderer2
  ) {}

  public ngOnInit(): void {
    if (this.clickOutsideEnabled) this.addListener();
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes['clickOutsideEnabled']) {
      if (this.clickOutsideEnabled) this.addListener();
      else this.removeListener();
    }
  }

  public ngOnDestroy(): void {
    this.removeListener();
  }

  private addListener(): void {
    // Small timeout to prevent immediate closing when opening
    setTimeout(() => {
      if (this.clickOutsideEnabled && !this.clickListener) {
        this.clickListener = this.renderer.listen(
          'document',
          'click',
          (event: Event) => {
            const targetElements = this.getTargetElements();

            const clickedElement = event.target as Element;

            const isOutside = targetElements.every(
              (element) => element && !element.contains(clickedElement)
            );

            if (isOutside) this.clickOutside.emit(event);
          }
        );
      }
    }, 0);
  }

  private removeListener(): void {
    if (this.clickListener) {
      this.clickListener();
      this.clickListener = undefined;
    }
  }

  private getTargetElements(): Element[] {
    const elements: Element[] = [];

    // Process provided elements list
    if (this.clickOutsideElements && this.clickOutsideElements.length > 0) {
      this.clickOutsideElements.forEach((input) => {
        const element = this.getElementFromInput(input);
        if (element) elements.push(element);
      });
    }

    // Fallback to host element if no elements provided
    if (elements.length === 0 && this.elementRef?.nativeElement) {
      elements.push(this.elementRef.nativeElement);
    }

    return elements;
  }

  private getElementFromInput(input: ElementRef | HTMLElement): Element | null {
    // Handle ElementRef (template reference variable)
    if (input && 'nativeElement' in input) return input.nativeElement;

    // Handle direct HTMLElement
    if (input instanceof Element) return input;

    return null;
  }
}
