import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IFabAction } from './fab-action.interface';

@Component({
  selector: 'lib-fab',
  templateUrl: './fab.component.html',
  styleUrls: ['./fab.component.scss'],
})
export class FabComponent {
  public isOpen = false;

  @Input() public items: IFabAction[] = [];

  @Output() public itemClick = new EventEmitter<IFabAction>();

  public toggleMenu(): void {
    this.isOpen = !this.isOpen;
  }

  public handleMenuClick(item: IFabAction): void {
    this.isOpen = false;
    this.itemClick.emit(item);
  }
}
