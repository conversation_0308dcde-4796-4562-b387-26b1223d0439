<div class="fixed bottom-8 right-8 z-50">
  <div class="relative" #fabContainer>
    <!-- Main FAB Button -->
    <button clickOutside [clickOutsideElements]="[fabContainer]" [clickOutsideEnabled]="isOpen" (clickOutside)="isOpen = false" (click)="toggleMenu()" class="w-16 h-16 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-2xl shadow-2xl flex items-center justify-center transition-all duration-500 hover:scale-110 focus:outline-none fab-main-button" [style.transform]="isOpen ? 'rotate(135deg) scale(1.15)' : 'rotate(0deg) scale(1)'">
      <svg class="w-8 h-8 text-white transition-transform duration-500 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" [style.transform]="isOpen ? 'rotate(-135deg)' : 'rotate(0deg)'">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
    </button>

    <!-- Sub Menu -->
    <div
      class="absolute bottom-20 left-1/2 -translate-x-1/2 space-y-4 transition-all duration-300 ease-out"
      [ngClass]="{
        'opacity-100 scale-100 pointer-events-auto': isOpen,
        'opacity-0 scale-0 pointer-events-none': !isOpen
      }"
    >
      <div
        *ngFor="let item of items; let i = index"
        class="flex items-center justify-end transition-all duration-300 group"
        [ngClass]="{
          'translate-x-0 opacity-100': isOpen,
          'translate-x-16 opacity-0': !isOpen
        }"
        [style.transition-delay]="isOpen ? i * 50 + 'ms' : '0ms'"
      >
        <div class="relative flex items-center justify-end hover:scale-110 hover:-translate-y-1 transition-all duration-300">
          <!-- Tooltip -->
          <span class="absolute right-14 top-1/2 -translate-y-1/2 px-3 py-2 bg-gray-900 bg-opacity-90 text-white text-sm font-medium rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap shadow-lg border border-gray-700 tracking-wider pointer-events-none">
            {{ item.label }}
          </span>
          <!-- Menu Button -->
          <button (click)="handleMenuClick(item)" class="focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-12 h-12 backdrop-blur-sm border border-gray-300 rounded-xl flex items-center justify-center transition-all duration-300 shadow-lg" [style.backgroundColor]="item.bgColor || '#fff'" [style.hover.backgroundColor]="item.hoverColor || '#f0f0f0'">
            <div *ngIf="item.customIcon" class="w-5 h-5 text-white flex items-center justify-center" [innerHTML]="item.customIcon | sanitize"></div>
            <i *ngIf="item.iconClass && !item.customIcon" [class]="item.iconClass"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
