import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { trigger, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'ng-omar-spinner',
  template: `
    <!-- Enhanced spinner with multiple variants and accessibility -->
    <div
      class="spinner-container flex items-center justify-center"
      [ngClass]="containerClass"
      [@fadeIn]="'in'"
      role="status"
      [attr.aria-label]="ariaLabel"
      [attr.aria-live]="'polite'"
    >
      <!-- Spinner Variants -->
      <div class="relative" [ngSwitch]="variant">
        <!-- Default Circular Spinner -->
        <div *ngSwitchCase="'circular'" class="spinner-circular">
          <!-- Background ring -->
          <div
            class="rounded-full border-solid opacity-20"
            [ngClass]="sizeClasses.ring"
            [style.border-color]="color"
          ></div>
          <!-- Animated progress ring -->
          <div
            class="absolute top-0 left-0 rounded-full border-solid border-transparent animate-spin"
            [ngClass]="sizeClasses.ring"
            [style.border-top-color]="color"
            [style.border-right-color]="
              showGradient ? gradientColor : 'transparent'
            "
          ></div>
          <!-- Center dot (optional) -->
          <div
            *ngIf="centerDot"
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full animate-pulse"
            [ngClass]="sizeClasses.dot"
            [style.background-color]="color"
          ></div>
        </div>

        <!-- Dots Spinner -->
        <div *ngSwitchCase="'dots'" class="spinner-dots flex space-x-1">
          <div
            *ngFor="let dot of dotsArray; let i = index"
            class="rounded-full animate-bounce"
            [ngClass]="sizeClasses.dotSize"
            [style.background-color]="color"
            [style.animation-delay]="i * 0.15 + 's'"
          ></div>
        </div>

        <!-- Bars Spinner -->
        <div
          *ngSwitchCase="'bars'"
          class="spinner-bars flex items-end space-x-1"
        >
          <div
            *ngFor="let bar of barsArray; let i = index"
            class="rounded-sm animate-pulse"
            [ngClass]="sizeClasses.barWidth"
            [style.background-color]="color"
            [style.height]="getBarHeight(i)"
            [style.animation-delay]="i * 0.1 + 's'"
            [style.animation-duration]="'0.8s'"
          ></div>
        </div>

        <!-- Pulse Spinner -->
        <div *ngSwitchCase="'pulse'" class="spinner-pulse relative">
          <div
            class="rounded-full animate-ping"
            [ngClass]="sizeClasses.pulse"
            [style.background-color]="color"
            style="opacity: 0.75;"
          ></div>
          <div
            class="absolute top-0 left-0 rounded-full"
            [ngClass]="sizeClasses.pulse"
            [style.background-color]="color"
          ></div>
        </div>

        <!-- Orbit Spinner -->
        <div *ngSwitchCase="'orbit'" class="spinner-orbit relative">
          <div
            class="rounded-full border-2 border-transparent animate-spin"
            [ngClass]="sizeClasses.orbit"
          >
            <div
              class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full"
              [ngClass]="sizeClasses.orbitDot"
              [style.background-color]="color"
            ></div>
            <div
              class="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-1/2 rounded-full"
              [ngClass]="sizeClasses.orbitDot"
              [style.background-color]="gradientColor"
            ></div>
          </div>
        </div>

        <!-- Default case (circular) -->
        <div *ngSwitchDefault class="spinner-circular">
          <div
            class="rounded-full border-solid opacity-20"
            [ngClass]="sizeClasses.ring"
            [style.border-color]="color"
          ></div>
          <div
            class="absolute top-0 left-0 rounded-full border-solid border-transparent animate-spin"
            [ngClass]="sizeClasses.ring"
            [style.border-top-color]="color"
          ></div>
        </div>

        <!-- Overlay pulse effect -->
        <div
          *ngIf="pulse && variant === 'circular'"
          class="absolute top-0 left-0 w-full h-full rounded-full animate-ping opacity-30"
          [style.background-color]="color"
        ></div>
      </div>

      <!-- Enhanced label with fade-in animation -->
      <div
        *ngIf="label || showPercentage"
        class="ml-3 text-gray-600 font-medium transition-opacity duration-300"
        [ngClass]="sizeClasses.text"
        [@slideIn]="'in'"
      >
        <span *ngIf="label" class="block">{{ label }}</span>
        <span
          *ngIf="showPercentage && progress !== undefined"
          class="text-sm opacity-75"
        >
          {{ progress }}%
        </span>
      </div>
    </div>

    <!-- Optional backdrop overlay -->
    <div
      *ngIf="overlay"
      class="fixed inset-0 bg-black bg-opacity-20 backdrop-blur-sm z-50 flex items-center justify-center"
      [@fadeIn]="'in'"
    >
      <div class="bg-white rounded-lg p-6 shadow-xl">
        <ng-container *ngTemplateOutlet="spinnerTemplate"></ng-container>
      </div>
    </div>

    <!-- Template for reusable spinner content -->
    <ng-template #spinnerTemplate>
      <ng-content></ng-content>
    </ng-template>
  `,
  styles: [
    `
      .spinner-container {
        min-height: fit-content;
        transition: all 0.3s ease;
      }

      .spinner-circular {
        position: relative;
      }

      .spinner-dots .animate-bounce {
        animation-iteration-count: infinite;
        animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
      }

      .spinner-bars > div {
        animation-name: bars-scale;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;
      }

      .spinner-orbit {
        animation-duration: 1.5s;
      }

      @keyframes bars-scale {
        0%,
        100% {
          transform: scaleY(1);
          opacity: 0.7;
        }
        50% {
          transform: scaleY(1.5);
          opacity: 1;
        }
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      /* Custom animations for smoother experience */
      .animate-spin {
        animation: spin 1s linear infinite;
      }

      /* Accessibility improvements */
      @media (prefers-reduced-motion: reduce) {
        .animate-spin,
        .animate-bounce,
        .animate-ping,
        .animate-pulse {
          animation: none;
        }

        .spinner-container {
          /* Provide static alternative for reduced motion */
          opacity: 0.8;
        }
      }

      /* Focus states for better accessibility */
      .spinner-container:focus-visible {
        outline: 2px solid currentColor;
        outline-offset: 2px;
      }

      /* High contrast mode support */
      @media (prefers-contrast: high) {
        .spinner-container {
          filter: contrast(1.2);
        }
      }
    `,
  ],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate(
          '300ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ opacity: 1, transform: 'scale(1)' })
        ),
      ]),
    ]),
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-10px)' }),
        animate(
          '300ms 150ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ opacity: 1, transform: 'translateX(0)' })
        ),
      ]),
    ]),
  ],
})
export class SpinnerComponent implements OnInit, OnDestroy {
  // Size and appearance
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'circular' | 'dots' | 'bars' | 'pulse' | 'orbit' =
    'circular';
  @Input() color = '#3B82F6'; // Default blue
  @Input() gradientColor = '#8B5CF6'; // Default purple for gradients
  @Input() showGradient = false;

  // Content and behavior
  @Input() label?: string;
  @Input() pulse = false;
  @Input() centerDot = false;
  @Input() overlay = false;
  @Input() progress?: number;
  @Input() showPercentage = false;

  // Styling and accessibility
  @Input() containerClass = '';
  @Input() ariaLabel = 'Loading';
  @Input() duration = 1000; // Animation duration in ms

  // Arrays for multi-element spinners
  dotsArray = [1, 2, 3];
  barsArray = [1, 2, 3, 4, 5];

  private progressInterval?: number;

  ngOnInit() {
    // Auto-increment progress if needed
    if (this.showPercentage && this.progress === undefined) {
      this.simulateProgress();
    }
  }

  ngOnDestroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
  }

  get sizeClasses() {
    const sizes = {
      xs: {
        ring: 'w-4 h-4 border-2',
        dot: 'w-1 h-1',
        dotSize: 'w-1 h-1',
        barWidth: 'w-1',
        pulse: 'w-4 h-4',
        orbit: 'w-4 h-4',
        orbitDot: 'w-1 h-1',
        text: 'text-xs',
      },
      sm: {
        ring: 'w-6 h-6 border-2',
        dot: 'w-1.5 h-1.5',
        dotSize: 'w-1.5 h-1.5',
        barWidth: 'w-1.5',
        pulse: 'w-6 h-6',
        orbit: 'w-6 h-6',
        orbitDot: 'w-1.5 h-1.5',
        text: 'text-sm',
      },
      md: {
        ring: 'w-8 h-8 border-3',
        dot: 'w-2 h-2',
        dotSize: 'w-2 h-2',
        barWidth: 'w-2',
        pulse: 'w-8 h-8',
        orbit: 'w-8 h-8',
        orbitDot: 'w-2 h-2',
        text: 'text-base',
      },
      lg: {
        ring: 'w-12 h-12 border-4',
        dot: 'w-3 h-3',
        dotSize: 'w-3 h-3',
        barWidth: 'w-3',
        pulse: 'w-12 h-12',
        orbit: 'w-12 h-12',
        orbitDot: 'w-3 h-3',
        text: 'text-lg',
      },
      xl: {
        ring: 'w-16 h-16 border-4',
        dot: 'w-4 h-4',
        dotSize: 'w-4 h-4',
        barWidth: 'w-4',
        pulse: 'w-16 h-16',
        orbit: 'w-16 h-16',
        orbitDot: 'w-4 h-4',
        text: 'text-xl',
      },
    };
    return sizes[this.size];
  }

  getBarHeight(index: number): string {
    const heights = ['0.5rem', '1rem', '1.5rem', '1rem', '0.5rem'];
    return heights[index] || '1rem';
  }

  private simulateProgress() {
    let currentProgress = 0;
    this.progress = 0;

    this.progressInterval = window.setInterval(() => {
      currentProgress += Math.random() * 15;
      if (currentProgress >= 100) {
        currentProgress = 100;
        clearInterval(this.progressInterval);
      }
      this.progress = Math.round(currentProgress);
    }, 200);
  }
}
