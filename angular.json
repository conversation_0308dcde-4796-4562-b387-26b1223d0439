{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "schematics": {"@schematics/angular:application": {"strict": true}, "@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:class": {"skipTests": true}}, "projects": {"ui": {"projectType": "library", "root": "projects/ui", "sourceRoot": "projects/ui/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/ui/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/ui/src/test.ts", "tsConfig": "projects/ui/tsconfig.spec.json", "karmaConfig": "projects/ui/karma.conf.js"}}}}, "sample": {"projectType": "application", "root": "projects/sample", "sourceRoot": "projects/sample/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/sample", "index": "projects/sample/src/index.html", "main": "projects/sample/src/main.ts", "polyfills": "projects/sample/src/polyfills.ts", "tsConfig": "projects/sample/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/sample/src/favicon.ico", "projects/sample/src/assets"], "styles": ["projects/sample/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/sample/src/environments/environment.ts", "with": "projects/sample/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "sample:build:production"}, "development": {"browserTarget": "sample:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "sample:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/sample/src/test.ts", "polyfills": "projects/sample/src/polyfills.ts", "tsConfig": "projects/sample/tsconfig.spec.json", "karmaConfig": "projects/sample/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["projects/sample/src/favicon.ico", "projects/sample/src/assets"], "styles": ["projects/sample/src/styles.scss"], "scripts": []}}}}, "core": {"projectType": "library", "root": "projects/core", "sourceRoot": "projects/core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/core/src/test.ts", "tsConfig": "projects/core/tsconfig.spec.json", "karmaConfig": "projects/core/karma.conf.js"}}}}, "dynamic-entity": {"projectType": "library", "root": "projects/dynamic-entity", "sourceRoot": "projects/dynamic-entity/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/dynamic-entity/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/dynamic-entity/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/dynamic-entity/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/dynamic-entity/src/test.ts", "tsConfig": "projects/dynamic-entity/tsconfig.spec.json", "karmaConfig": "projects/dynamic-entity/karma.conf.js"}}}}, "query-builder": {"projectType": "library", "root": "projects/query-builder", "sourceRoot": "projects/query-builder/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/query-builder/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/query-builder/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/query-builder/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/query-builder/src/test.ts", "tsConfig": "projects/query-builder/tsconfig.spec.json", "karmaConfig": "projects/query-builder/karma.conf.js"}}}}, "crud-core": {"projectType": "library", "root": "projects/crud-core", "sourceRoot": "projects/crud-core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/crud-core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/crud-core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/crud-core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/crud-core/src/test.ts", "tsConfig": "projects/crud-core/tsconfig.spec.json", "karmaConfig": "projects/crud-core/karma.conf.js"}}}}, "dynamic-forms": {"projectType": "library", "root": "projects/dynamic-forms", "sourceRoot": "projects/dynamic-forms/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/dynamic-forms/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/dynamic-forms/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/dynamic-forms/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/dynamic-forms/src/test.ts", "tsConfig": "projects/dynamic-forms/tsconfig.spec.json", "karmaConfig": "projects/dynamic-forms/karma.conf.js"}}}}}, "defaultProject": "ui"}